# Inventory Report Implementation Steps

## Overview

This document outlines the step-by-step implementation plan for creating an inventory report system that follows the same structure as the sales report system while leveraging the efficient data processing patterns from the medicine register system.

## Requirements

### Report Specifications
- **Report Type:** Rolling total from medicine's register
- **Frequency:** Daily
- **Metrics:** Date, SKU, Opening balance, Quantity dispensed, Closing balance

### Technical Requirements
- Follow sales report structure and patterns
- Generate image reports for Slack integration
- Implement caching strategy (4-hour invalidation)
- Incremental data processing
- Real-time balance updates
- WordPress API integration

## Implementation Steps

### Phase 1: Project Structure Setup

#### Step 1.1: Create Directory Structure
```bash
# Create inventory controller directory
mkdir -p apps/api/src/controllers/inventory

# Create inventory service directory
mkdir -p apps/api/src/services

# Create inventory routes directory
mkdir -p apps/api/src/routes

# Create inventory templates directory
mkdir -p apps/api/src/templates/inventory
```

#### Step 1.2: Create Base Files
- `apps/api/src/controllers/inventory/index.ts` - Main controller
- `apps/api/src/services/inventoryReporting.service.ts` - Core service
- `apps/api/src/routes/inventory.route.ts` - Route definitions
- `apps/api/src/templates/inventory/daily-report.html` - HTML template

### Phase 2: Core Service Implementation

#### Step 2.1: Create Inventory Reporting Service
**File:** `apps/api/src/services/inventoryReporting.service.ts`

**Key Features:**
- Daily inventory report generation
- Image generation using HTML templates
- Slack integration
- Data caching and processing
- Error handling and logging

**Methods to Implement:**
```typescript
class InventoryReportingService {
  // Core report generation
  async generateDailyInventoryReport(): Promise<InventoryReport>
  async generateDailyInventoryReportAsImage(): Promise<Buffer>
  
  // Data processing
  async getInventoryDataForDateRange(startDate: string, endDate: string): Promise<InventoryData>
  async processInventoryData(): Promise<ProcessedInventoryData>
  
  // Slack integration
  async sendDailyInventoryReport(): Promise<void>
  async sendInventoryReportAsImage(): Promise<void>
  
  // Caching
  async getCachedInventoryData(): Promise<CachedInventoryData>
  async cacheInventoryData(data: InventoryData): Promise<void>
  
  // Testing
  async generateSampleInventoryData(): Promise<InventoryData>
  async sendTestReport(): Promise<void>
}
```

#### Step 2.2: Create Data Types and Interfaces
**File:** `apps/api/src/types/inventory.ts`

**Interfaces to Define:**
```typescript
interface InventoryReport {
  reportDate: string;
  totalSKUs: number;
  totalOpeningBalance: number;
  totalQuantityDispensed: number;
  totalClosingBalance: number;
  skuDetails: SKUDetail[];
  metadata: ReportMetadata;
}

interface SKUDetail {
  sku: string;
  productName: string;
  openingBalance: number;
  quantityDispensed: number;
  closingBalance: number;
  unit: string;
  lastUpdated: string;
}

interface ProcessedInventoryData {
  productCentricData: ProductCentricData;
  orders: OrderData;
  balances: BalanceData;
  lastPullDate: string;
}
```

### Phase 3: Controller Implementation

#### Step 3.1: Create Inventory Controller
**File:** `apps/api/src/controllers/inventory/index.ts`

**Endpoints to Implement:**
```typescript
// Core report endpoints
export const triggerDailyInventoryReport: RequestHandler
export const triggerDailyInventoryReportImage: RequestHandler

// Data access endpoints
export const getInventoryData: RequestHandler
export const getInventoryReportStatus: RequestHandler

// Testing endpoints
export const testInventoryReport: RequestHandler
export const generateTestInventoryData: RequestHandler

// Monitoring endpoints
export const getInventorySystemHealth: RequestHandler
export const getInventorySyncStatus: RequestHandler
```

#### Step 3.2: Implement Core Logic
**Key Implementation Details:**
- Follow exact structure of sales controller
- Include proper error handling and logging
- Add request validation
- Implement response formatting
- Add authentication middleware

### Phase 4: Route Configuration

#### Step 4.1: Create Inventory Routes
**File:** `apps/api/src/routes/inventory.route.ts`

**Route Definitions:**
```typescript
const router = express.Router();
const currentVersion = 'v1.0';

// Core report endpoints
router.post(`/${currentVersion}/trigger-daily-report`, triggerDailyInventoryReport);
router.post(`/${currentVersion}/trigger-daily-report-image`, triggerDailyInventoryReportImage);

// Data access endpoints
router.get(`/${currentVersion}/data`, getInventoryData);
router.get(`/${currentVersion}/status`, getInventoryReportStatus);

// Testing endpoints
router.post(`/${currentVersion}/test-report`, testInventoryReport);
router.post(`/${currentVersion}/generate-test-data`, generateTestInventoryData);

// Monitoring endpoints
router.get(`/${currentVersion}/health`, getInventorySystemHealth);
router.get(`/${currentVersion}/sync-status`, getInventorySyncStatus);
```

#### Step 4.2: Register Routes in Main App
**File:** `apps/api/src/app.ts` or main route file

```typescript
import inventoryRoutes from './routes/inventory.route';
app.use('/api/inventory', inventoryRoutes);
```

### Phase 5: HTML Template Creation

#### Step 5.1: Create Daily Report Template
**File:** `apps/api/src/templates/inventory/daily-report.html`

**Template Features:**
- Professional inventory report layout
- Responsive design for image generation
- Include all required metrics:
  - Date
  - SKU
  - Opening balance
  - Quantity dispensed
  - Closing balance
- Company branding and styling
- Summary statistics
- Detailed SKU breakdown

#### Step 5.2: Template Structure
```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Daily Inventory Report</title>
  <style>
    /* Professional styling for image generation */
  </style>
</head>
<body>
  <div class="report-container">
    <header class="report-header">
      <h1>Daily Inventory Report</h1>
      <div class="report-meta">
        <span>Date: {{reportDate}}</span>
        <span>Generated: {{generatedAt}}</span>
      </div>
    </header>
    
    <section class="summary-stats">
      <div class="stat-card">
        <h3>Total SKUs</h3>
        <span>{{totalSKUs}}</span>
      </div>
      <div class="stat-card">
        <h3>Opening Balance</h3>
        <span>{{totalOpeningBalance}}</span>
      </div>
      <div class="stat-card">
        <h3>Quantity Dispensed</h3>
        <span>{{totalQuantityDispensed}}</span>
      </div>
      <div class="stat-card">
        <h3>Closing Balance</h3>
        <span>{{totalClosingBalance}}</span>
      </div>
    </section>
    
    <section class="sku-details">
      <table class="inventory-table">
        <thead>
          <tr>
            <th>SKU</th>
            <th>Product Name</th>
            <th>Opening Balance</th>
            <th>Quantity Dispensed</th>
            <th>Closing Balance</th>
            <th>Unit</th>
          </tr>
        </thead>
        <tbody>
          {{#each skuDetails}}
          <tr>
            <td>{{sku}}</td>
            <td>{{productName}}</td>
            <td>{{openingBalance}}</td>
            <td>{{quantityDispensed}}</td>
            <td>{{closingBalance}}</td>
            <td>{{unit}}</td>
          </tr>
          {{/each}}
        </tbody>
      </table>
    </section>
  </div>
</body>
</html>
```

### Phase 6: Database Integration

#### Step 6.1: Leverage Existing Tables
**Tables to Use:**
- `productstock` - Current product balances
- `medregistermanagement` - Caching and historical data
- `orders` - Order data from WordPress API

#### Step 6.2: Create Inventory-Specific Tables (if needed)
```sql
-- Inventory cache table
CREATE TABLE IF NOT EXISTS inventory_cache (
  id SERIAL PRIMARY KEY,
  cache_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  report_data JSONB,
  last_pull_date TIMESTAMP,
  is_valid BOOLEAN DEFAULT true
);

-- Inventory report history
CREATE TABLE IF NOT EXISTS inventory_reports (
  id SERIAL PRIMARY KEY,
  report_date DATE,
  report_data JSONB,
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  sent_to_slack BOOLEAN DEFAULT false
);
```

### Phase 7: Configuration Setup

#### Step 7.1: Add Configuration Variables
**File:** `apps/api/src/config/index.ts`

```typescript
// Inventory reporting configuration
inventoryReportingEnabled: process.env.INVENTORY_REPORTING_ENABLED === 'true',
inventorySlackChannel: process.env.INVENTORY_SLACK_CHANNEL || '#inventory-reports',
inventoryDataRetentionDays: parseInt(process.env.INVENTORY_DATA_RETENTION_DAYS || '30'),
inventoryCacheHours: parseInt(process.env.INVENTORY_CACHE_HOURS || '4'),
```

#### Step 7.2: Environment Variables
**File:** `.env`

```bash
# Inventory Reporting
INVENTORY_REPORTING_ENABLED=true
INVENTORY_SLACK_CHANNEL=#inventory-reports
INVENTORY_DATA_RETENTION_DAYS=30
INVENTORY_CACHE_HOURS=4
```

### Phase 8: Integration with Existing Systems

#### Step 8.1: WordPress API Integration
**Leverage Existing Patterns:**
- Use `WordPressUtils.getOrders()` for order data
- Follow medicine register caching strategy
- Implement incremental processing

#### Step 8.2: Slack Integration
**Follow Sales Report Pattern:**
- Use existing Slack service
- Implement image upload
- Add proper error handling

#### Step 8.3: Caching Strategy
**Implement Medicine Register Pattern:**
- 4-hour cache invalidation
- Store in `medregistermanagement` table
- Incremental data processing

### Phase 9: Testing and Validation

#### Step 9.1: Unit Tests
**Create Test Files:**
- `apps/api/src/controllers/inventory/__tests__/index.test.ts`
- `apps/api/src/services/__tests__/inventoryReporting.service.test.ts`

#### Step 9.2: Integration Tests
**Test Scenarios:**
- Daily report generation
- Image generation
- Slack integration
- Data caching
- Error handling

#### Step 9.3: Manual Testing
**Test Endpoints:**
```bash
# Test daily report generation
curl -X POST http://localhost:3000/api/inventory/v1/trigger-daily-report

# Test image generation
curl -X POST http://localhost:3000/api/inventory/v1/trigger-daily-report-image

# Test data retrieval
curl -X GET "http://localhost:3000/api/inventory/v1/data?startDate=2024-01-01&endDate=2024-01-31"

# Test system status
curl -X GET http://localhost:3000/api/inventory/v1/status
```

### Phase 10: Deployment and Monitoring

#### Step 10.1: Cron Job Setup
**Add to crontab:**
```bash
# Daily inventory report at 9:00 AM
0 9 * * * curl -X POST https://your-domain.com/api/inventory/v1/trigger-daily-report

# Daily inventory report image at 9:15 AM
15 9 * * * curl -X POST https://your-domain.com/api/inventory/v1/trigger-daily-report-image
```

#### Step 10.2: Monitoring Setup
**Monitor:**
- Report generation success/failure
- Image generation performance
- Slack delivery status
- Data processing times
- Cache hit/miss rates

#### Step 10.3: Error Handling
**Implement:**
- Automatic retry mechanisms
- Error notifications
- Fallback to cached data
- Detailed error logging

## Success Criteria

### Functional Requirements
- [ ] Daily inventory reports generated automatically
- [ ] Image reports sent to Slack
- [ ] All required metrics included (Date, SKU, Opening balance, Quantity dispensed, Closing balance)
- [ ] Caching strategy implemented (4-hour invalidation)
- [ ] Incremental data processing working
- [ ] Real-time balance updates functional

### Technical Requirements
- [ ] Follows sales report structure exactly
- [ ] Leverages medicine register patterns
- [ ] Proper error handling and logging
- [ ] Comprehensive testing coverage
- [ ] Performance optimized
- [ ] Scalable architecture

### Quality Requirements
- [ ] Professional HTML template design
- [ ] Accurate data calculations
- [ ] Reliable Slack integration
- [ ] Robust error handling
- [ ] Comprehensive documentation

## Timeline Estimate

- **Phase 1-2:** 2-3 days (Core service and controller)
- **Phase 3-4:** 1-2 days (Routes and integration)
- **Phase 5:** 1 day (HTML template)
- **Phase 6-7:** 1 day (Database and configuration)
- **Phase 8:** 1-2 days (System integration)
- **Phase 9:** 1-2 days (Testing)
- **Phase 10:** 1 day (Deployment and monitoring)

**Total Estimated Time:** 8-12 days

## Dependencies

- Existing sales report system (for structure reference)
- Medicine register system (for data processing patterns)
- WordPress API integration
- Slack integration
- Database access
- Image generation libraries

## Risk Mitigation

1. **Data Accuracy:** Implement comprehensive validation and testing
2. **Performance:** Use caching and incremental processing
3. **Integration Issues:** Follow existing patterns and test thoroughly
4. **Slack Delivery:** Implement retry mechanisms and monitoring
5. **Data Loss:** Implement backup and recovery procedures

## Next Steps

1. Review and approve this implementation plan
2. Set up development environment
3. Begin with Phase 1 (Project Structure Setup)
4. Implement incrementally with testing at each phase
5. Deploy and monitor in production environment 
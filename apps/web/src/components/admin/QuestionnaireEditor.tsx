import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Chip,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Switch,
  FormControlLabel,
  Divider,
  Tabs,
  Tab
} from '@mui/material';
import { ExpandMore, Edit, Delete, Add, Save, ArrowBack, Info, ViewModule as SectionsIcon, List as QuestionsIcon } from '@mui/icons-material';
import axiosInstance from '../../services/axios';
import routes from '../../services/apiRoutes';
import { QuestionnaireConfig, QuestionConfig, AnswerOption, SectionConfig } from '../../types/questionnaire-admin';
import SectionManager from './SectionManager';
import SpecialLogicQuestionEditor from './SpecialLogicQuestionEditor';

interface QuestionnaireEditorProps {
  questionnaireId: string;
  onBack: () => void;
}

// Helper functions
const getMaxScore = (question: QuestionConfig): number => {
  if (!question.contributesToScore) return 0;

  switch (question.type) {
    case 'radio':
    case 'checkbox':
    case 'dropdown':
    case 'special_logic':
      return question.answerOptions ? Math.max(...question.answerOptions.map(a => a.score)) : 0;
    case 'slider':
      return question.sliderConfig ? Math.max(...Object.values(question.sliderConfig.scoreMapping)) : 0;
    case 'text':
    case 'date':
    default:
      return 0;
  }
};

const renderQuestionDetails = (question: QuestionConfig) => {
  switch (question.type) {
    case 'radio':
    case 'checkbox':
    case 'dropdown':
    case 'special_logic':
      return (
        <Box>
          {question.answerOptions?.map((option, optIndex) => (
            <Box key={optIndex} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>{option.label}</Typography>
              {question.contributesToScore && <Chip label={`${option.score} pts`} size="small" />}
            </Box>
          ))}
          {question.type === 'special_logic' && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="caption">
                Special logic: {question.specialLogic?.description || 'Custom scoring implementation'}
              </Typography>
            </Alert>
          )}
        </Box>
      );

    case 'slider':
      return (
        <Box>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Slider range: {question.sliderConfig?.min} - {question.sliderConfig?.max}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Score mapping configured for effectiveness rating
          </Typography>
        </Box>
      );

    case 'text':
      return (
        <Box>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Text input (max {question.textFieldConfig?.maxLength} characters)
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Purpose: {question.textFieldConfig?.purpose}
          </Typography>
          <Alert severity="info" sx={{ mt: 1 }}>
            <Typography variant="caption">
              Text fields are used for medical review and do not contribute to scoring
            </Typography>
          </Alert>
        </Box>
      );

    case 'date':
      return (
        <Box>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Date field {question.dateConfig?.required ? '(required)' : '(optional)'}
          </Typography>
          {question.dateConfig?.minAge && (
            <Typography variant="caption" color="text.secondary">
              Minimum age: {question.dateConfig.minAge}
            </Typography>
          )}
        </Box>
      );

    default:
      return <Typography variant="body2">Unknown question type</Typography>;
  }
};

const QuestionnaireEditor: React.FC<QuestionnaireEditorProps> = ({ questionnaireId, onBack }) => {
  const [config, setConfig] = useState<QuestionnaireConfig | null>(null);
  const [originalConfig, setOriginalConfig] = useState<QuestionnaireConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingQuestion, setEditingQuestion] = useState<{ open: boolean; question?: QuestionConfig; index?: number }>({ open: false });
  const [hasChanges, setHasChanges] = useState(false);
  const [currentTab, setCurrentTab] = useState(0); // 0 = Questions, 1 = Sections
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null);

  useEffect(() => {
    fetchConfig();
  }, [questionnaireId]);

  const fetchConfig = async () => {
    try {
      const response = await axiosInstance.get(`${routes.GET_QUESTIONNAIRE_CONFIG}/${questionnaireId}`);
      const configData = response.data.data;
      setConfig(configData);
      setOriginalConfig(JSON.parse(JSON.stringify(configData))); // Deep copy for original state
    } catch (error) {
      console.error('Failed to fetch config:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!config) return;

    try {
      await axiosInstance.put(`${routes.UPDATE_QUESTIONNAIRE_CONFIG}/${questionnaireId}`, {
        name: config.name,
        maxScore: config.maxScore,
        threshold: config.threshold,
        questions: config.questions
      });
      setHasChanges(false);
      // Update original config to reflect saved state
      setOriginalConfig(JSON.parse(JSON.stringify(config)));
    } catch (error) {
      console.error('Failed to save config:', error);
    }
  };

  const handleCancel = () => {
    if (!originalConfig) return;

    // Revert to original config
    setConfig(JSON.parse(JSON.stringify(originalConfig)));
    setHasChanges(false);
  };

  const handleQuestionSave = (question: QuestionConfig, index?: number) => {
    if (!config || !config.questions) return;

    const newQuestions = [...config.questions];
    if (index !== undefined) {
      newQuestions[index] = question;
    } else {
      newQuestions.push({ ...question, order: newQuestions.length + 1 });
    }

    // Recalculate max score using the helper function
    const newMaxScore = newQuestions.reduce((sum, q) => sum + getMaxScore(q), 0);

    setConfig({ ...config, questions: newQuestions, maxScore: newMaxScore });
    setHasChanges(true);
    setEditingQuestion({ open: false });
  };

  const handleDeleteQuestion = (index: number) => {
    if (!config || !config.questions) return;

    const newQuestions = config.questions.filter((_, i) => i !== index);
    // Recalculate max score using the helper function
    const newMaxScore = newQuestions.reduce((sum, q) => sum + getMaxScore(q), 0);

    setConfig({ ...config, questions: newQuestions, maxScore: newMaxScore });
    setHasChanges(true);
  };

  const handleSectionsChange = (sections: SectionConfig[]) => {
    if (!config) return;
    setConfig({ ...config, sections });
    setHasChanges(true);
  };

  const handleSectionEdit = (sectionId: string) => {
    setSelectedSectionId(sectionId);
    setCurrentTab(0); // Switch to questions tab to edit section questions
  };

  const handleAddQuestionToSection = (sectionId: string) => {
    if (!config) return;

    // Create a new question template with the section ID (don't add to config yet)
    const newQuestion: QuestionConfig = {
      key: `question_${Date.now()}`,
      text: '',
      type: 'radio',
      order: (config.questions?.length || 0) + 1,
      contributesToScore: true,
      answerOptions: [{ value: '', label: '', score: 0 }],
      sectionId: sectionId
    };

    // Open the question editor for the new question (without adding to config)
    setEditingQuestion({
      open: true,
      question: newQuestion,
      index: undefined // undefined means it's a new question, not editing existing
    });

    // Switch to questions tab and select the section
    setSelectedSectionId(sectionId);
    setCurrentTab(0);
  };

  const getFilteredQuestions = () => {
    if (!config || !config.questions) return [];

    if (selectedSectionId) {
      return config.questions.filter(q => q.sectionId === selectedSectionId);
    }

    return config.questions;
  };

  if (loading || !config) return <Typography>Loading...</Typography>;

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={onBack} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4">{config.name} Configuration</Typography>
        {hasChanges && <Chip label="Unsaved Changes" color="warning" sx={{ ml: 2 }} />}
      </Box>

      {/* Tab Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
          <Tab
            label="Questions"
            icon={<QuestionsIcon />}
            iconPosition="start"
          />
          <Tab
            label="Sections"
            icon={<SectionsIcon />}
            iconPosition="start"
            disabled={!config.sections || config.sections.length === 0}
          />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Settings</Typography>
              <TextField
                fullWidth
                label="Name"
                value={config.name}
                onChange={(e) => {
                  setConfig({ ...config, name: e.target.value });
                  setHasChanges(true);
                }}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                type="number"
                label="Threshold"
                value={config.threshold}
                onChange={(e) => {
                  setConfig({ ...config, threshold: parseInt(e.target.value) || 0 });
                  setHasChanges(true);
                }}
                sx={{ mb: 2 }}
              />
              <Typography variant="body2" color="text.secondary">
                Max Score: {config.maxScore || 0} (calculated)
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Threshold: {config.maxScore && config.threshold !== undefined
                  ? Math.round((Number(config.threshold) / Number(config.maxScore)) * 100)
                  : 0}%
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<Save />}
                  onClick={handleSave}
                  disabled={!hasChanges}
                  sx={{ flex: 1 }}
                >
                  Save Changes
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleCancel}
                  disabled={!hasChanges}
                  sx={{ flex: 1 }}
                >
                  Cancel
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="h6">Questions ({getFilteredQuestions().length})</Typography>
              {selectedSectionId && (
                <Chip
                  label={`Section: ${config.sections?.find(s => s.id === selectedSectionId)?.title || 'Unknown'}`}
                  onDelete={() => setSelectedSectionId(null)}
                  color="primary"
                  variant="outlined"
                />
              )}
            </Box>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => setEditingQuestion({
                open: true,
                question: selectedSectionId ? {
                  key: `question_${Date.now()}`,
                  text: '',
                  type: 'radio',
                  order: (config.questions?.length || 0) + 1,
                  contributesToScore: true,
                  answerOptions: [{ value: '', label: '', score: 0 }],
                  sectionId: selectedSectionId
                } : undefined
              })}
            >
              Add Question
            </Button>
          </Box>

          {/* Section Filter */}
          {config.sections && config.sections.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <FormControl size="small" sx={{ minWidth: 200 }}>
                <InputLabel>Filter by Section</InputLabel>
                <Select
                  value={selectedSectionId || ''}
                  onChange={(e) => setSelectedSectionId(e.target.value || null)}
                  label="Filter by Section"
                >
                  <MenuItem value="">
                    <em>All Sections</em>
                  </MenuItem>
                  {config.sections.map((section) => (
                    <MenuItem key={section.id} value={section.id}>
                      {section.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}

          {getFilteredQuestions().map((question, index) => (
            <Accordion key={index} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                  <Typography sx={{ flexGrow: 1 }}>
                    {index + 1}. {question.text}
                  </Typography>
                  <Chip
                    label={question.type.replace('_', ' ')}
                    size="small"
                    sx={{ mr: 1 }}
                    color={question.type === 'special_logic' ? 'warning' : question.type === 'text' ? 'info' : 'default'}
                  />
                  {question.contributesToScore && (
                    <Chip
                      label={`Max: ${getMaxScore(question)}`}
                      size="small"
                      color="primary"
                    />
                  )}
                  {!question.contributesToScore && (
                    <Chip
                      label="Non-scoring"
                      size="small"
                      color="secondary"
                    />
                  )}
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box sx={{ flexGrow: 1 }}>
                    {renderQuestionDetails(question)}
                  </Box>
                  <Box>
                    <IconButton
                      size="small"
                      onClick={() => setEditingQuestion({ open: true, question, index })}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteQuestion(index)}
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}
        </Grid>
      </Grid>
      )}

      {/* Sections Tab */}
      {currentTab === 1 && config.sections && (
        <SectionManager
          questionnaireId={questionnaireId}
          sections={config.sections}
          questions={config.questions || []}
          onSectionsChange={handleSectionsChange}
          onSectionEdit={handleSectionEdit}
          onAddQuestionToSection={handleAddQuestionToSection}
        />
      )}

      <QuestionEditor
        open={editingQuestion.open}
        question={editingQuestion.question}
        index={editingQuestion.index}
        onSave={handleQuestionSave}
        onClose={() => setEditingQuestion({ open: false })}
        config={config}
      />
    </Box>
  );
};

interface QuestionEditorProps {
  open: boolean;
  question?: QuestionConfig;
  index?: number;
  onSave: (question: QuestionConfig, index?: number) => void;
  onClose: () => void;
  config?: QuestionnaireConfig;
}

const QuestionEditor: React.FC<QuestionEditorProps> = ({ open, question, index, onSave, onClose, config }) => {
  const [editQuestion, setEditQuestion] = useState<QuestionConfig>({
    key: '',
    text: '',
    type: 'radio',
    order: 1,
    contributesToScore: true,
    answerOptions: [{ value: '', label: '', score: 0 }]
  });

  useEffect(() => {
    if (question) {
      setEditQuestion(question);
    } else {
      setEditQuestion({
        key: '',
        text: '',
        type: 'radio',
        order: 1,
        contributesToScore: true,
        answerOptions: [{ value: '', label: '', score: 0 }]
      });
    }
  }, [question, open]);

  const addAnswerOption = () => {
    if (!editQuestion.answerOptions) {
      setEditQuestion({ ...editQuestion, answerOptions: [{ value: '', label: '', score: 0 }] });
      return;
    }
    setEditQuestion({
      ...editQuestion,
      answerOptions: [...editQuestion.answerOptions, { value: '', label: '', score: 0 }]
    });
  };

  const updateAnswerOption = (index: number, field: keyof AnswerOption, value: string | number) => {
    if (!editQuestion.answerOptions) return;
    const newOptions = [...editQuestion.answerOptions];
    newOptions[index] = { ...newOptions[index], [field]: value };
    setEditQuestion({ ...editQuestion, answerOptions: newOptions });
  };

  const removeAnswerOption = (index: number) => {
    if (!editQuestion.answerOptions || editQuestion.answerOptions.length <= 1) return;
    const newOptions = editQuestion.answerOptions.filter((_, i) => i !== index);
    setEditQuestion({ ...editQuestion, answerOptions: newOptions });
  };

  const handleTypeChange = (newType: QuestionConfig['type']) => {
    const baseQuestion = {
      ...editQuestion,
      type: newType,
      contributesToScore: newType !== 'text'
    };

    // Reset type-specific configurations
    delete baseQuestion.answerOptions;
    delete baseQuestion.sliderConfig;
    delete baseQuestion.textFieldConfig;
    delete baseQuestion.specialLogic;
    delete baseQuestion.dateConfig;

    // Set defaults based on type
    switch (newType) {
      case 'radio':
      case 'checkbox':
      case 'dropdown':
        baseQuestion.answerOptions = [{ value: '', label: '', score: 0 }];
        break;
      case 'slider':
        baseQuestion.sliderConfig = {
          min: 1,
          max: 10,
          scoreMapping: { '1': 0, '10': 10 }
        };
        break;
      case 'text':
        baseQuestion.textFieldConfig = {
          maxLength: 500,
          required: false,
          placeholder: '',
          purpose: 'Medical review'
        };
        break;
      case 'date':
        baseQuestion.dateConfig = {
          required: true,
          minAge: 18
        };
        break;
      case 'special_logic':
        baseQuestion.answerOptions = [{ value: '', label: '', score: 0 }];
        baseQuestion.specialLogic = {
          questionKey: baseQuestion.key,
          description: 'Custom scoring logic',
          codeSnippet: 'Custom logic implementation required',
          testCases: [],
          requiresDeveloper: true,
          lastModified: new Date()
        };
        break;
    }

    setEditQuestion(baseQuestion);
  };

  const renderQuestionTypeSpecificFields = () => {
    switch (editQuestion.type) {
      case 'radio':
      case 'checkbox':
      case 'dropdown':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Answer Options</Typography>
            {editQuestion.answerOptions?.map((option, index) => (
              <Box key={index} sx={{ display: 'flex', gap: 2, mb: 2, alignItems: 'center' }}>
                <TextField
                  label="Value"
                  value={option.value}
                  onChange={(e) => updateAnswerOption(index, 'value', e.target.value)}
                />
                <TextField
                  label="Label"
                  value={option.label}
                  onChange={(e) => updateAnswerOption(index, 'label', e.target.value)}
                  sx={{ flexGrow: 1 }}
                />
                <TextField
                  label="Score"
                  type="number"
                  value={option.score}
                  onChange={(e) => updateAnswerOption(index, 'score', parseInt(e.target.value) || 0)}
                  sx={{ width: 100 }}
                />
                <IconButton onClick={() => removeAnswerOption(index)} disabled={(editQuestion.answerOptions?.length || 0) <= 1}>
                  <Delete />
                </IconButton>
              </Box>
            ))}
            <Button startIcon={<Add />} onClick={addAnswerOption}>
              Add Option
            </Button>
          </Box>
        );

      case 'slider':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Slider Configuration</Typography>
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                label="Min Value"
                type="number"
                value={editQuestion.sliderConfig?.min || 1}
                onChange={(e) => setEditQuestion({
                  ...editQuestion,
                  sliderConfig: { ...editQuestion.sliderConfig!, min: parseInt(e.target.value) || 1 }
                })}
              />
              <TextField
                label="Max Value"
                type="number"
                value={editQuestion.sliderConfig?.max || 10}
                onChange={(e) => setEditQuestion({
                  ...editQuestion,
                  sliderConfig: { ...editQuestion.sliderConfig!, max: parseInt(e.target.value) || 10 }
                })}
              />
            </Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>Score Mapping (Value → Score)</Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              Configure how slider values map to scores. Lower effectiveness typically gets higher scores.
            </Alert>
          </Box>
        );

      case 'text':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Text Field Configuration</Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Info />
                Text fields are non-scoring and used for medical review purposes.
              </Box>
            </Alert>
            <TextField
              fullWidth
              label="Max Length"
              type="number"
              value={editQuestion.textFieldConfig?.maxLength || 500}
              onChange={(e) => setEditQuestion({
                ...editQuestion,
                textFieldConfig: { ...editQuestion.textFieldConfig!, maxLength: parseInt(e.target.value) || 500 }
              })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Placeholder"
              value={editQuestion.textFieldConfig?.placeholder || ''}
              onChange={(e) => setEditQuestion({
                ...editQuestion,
                textFieldConfig: { ...editQuestion.textFieldConfig!, placeholder: e.target.value }
              })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Purpose"
              value={editQuestion.textFieldConfig?.purpose || 'Medical review'}
              onChange={(e) => setEditQuestion({
                ...editQuestion,
                textFieldConfig: { ...editQuestion.textFieldConfig!, purpose: e.target.value }
              })}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={editQuestion.textFieldConfig?.required || false}
                  onChange={(e) => setEditQuestion({
                    ...editQuestion,
                    textFieldConfig: { ...editQuestion.textFieldConfig!, required: e.target.checked }
                  })}
                />
              }
              label="Required Field"
            />
          </Box>
        );

      case 'special_logic':
        return (
          <SpecialLogicQuestionEditor
            question={editQuestion}
            onUpdate={setEditQuestion}
            readOnly={false}
          />
        );

      case 'date':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Date Field Configuration</Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              Date fields are typically used for age validation and demographic data.
            </Alert>
            <TextField
              fullWidth
              label="Minimum Age"
              type="number"
              value={editQuestion.dateConfig?.minAge || 18}
              onChange={(e) => setEditQuestion({
                ...editQuestion,
                dateConfig: { ...editQuestion.dateConfig!, minAge: parseInt(e.target.value) || 18 }
              })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Maximum Age"
              type="number"
              value={editQuestion.dateConfig?.maxAge || ''}
              onChange={(e) => setEditQuestion({
                ...editQuestion,
                dateConfig: { ...editQuestion.dateConfig!, maxAge: parseInt(e.target.value) || undefined }
              })}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={editQuestion.dateConfig?.required || false}
                  onChange={(e) => setEditQuestion({
                    ...editQuestion,
                    dateConfig: { ...editQuestion.dateConfig!, required: e.target.checked }
                  })}
                />
              }
              label="Required Field"
            />
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>{question ? 'Edit Question' : 'Add Question'}</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label="Question Key"
          value={editQuestion.key}
          onChange={(e) => setEditQuestion({ ...editQuestion, key: e.target.value })}
          sx={{ mb: 2, mt: 1 }}
        />
        <TextField
          fullWidth
          label="Question Text"
          value={editQuestion.text}
          onChange={(e) => setEditQuestion({ ...editQuestion, text: e.target.value })}
          sx={{ mb: 2 }}
        />
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Question Type</InputLabel>
          <Select
            value={editQuestion.type}
            onChange={(e) => handleTypeChange(e.target.value as QuestionConfig['type'])}
            label="Question Type"
          >
            <MenuItem value="radio">Radio Button</MenuItem>
            <MenuItem value="checkbox">Checkbox</MenuItem>
            <MenuItem value="dropdown">Dropdown</MenuItem>
            <MenuItem value="slider">Slider (1-10 Scale)</MenuItem>
            <MenuItem value="text">Text Input</MenuItem>
            <MenuItem value="date">Date Field</MenuItem>
            <MenuItem value="special_logic">Special Logic</MenuItem>
          </Select>
        </FormControl>

        {/* Section Selector */}
        {config && config.sections && config.sections.length > 0 && (
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Section</InputLabel>
            <Select
              value={editQuestion.sectionId || ''}
              onChange={(e) => setEditQuestion({ ...editQuestion, sectionId: e.target.value || undefined })}
              label="Section"
            >
              <MenuItem value="">
                <em>No Section</em>
              </MenuItem>
              {config.sections.map((section: SectionConfig) => (
                <MenuItem key={section.id} value={section.id}>
                  {section.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <TextField
            label="Order"
            type="number"
            value={editQuestion.order}
            onChange={(e) => setEditQuestion({ ...editQuestion, order: parseInt(e.target.value) || 1 })}
            sx={{ width: 120 }}
          />
          <FormControlLabel
            control={
              <Switch
                checked={editQuestion.contributesToScore}
                onChange={(e) => setEditQuestion({ ...editQuestion, contributesToScore: e.target.checked })}
                disabled={editQuestion.type === 'text'}
              />
            }
            label="Contributes to Score"
          />
        </Box>

        <Divider sx={{ my: 3 }} />

        {renderQuestionTypeSpecificFields()}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={() => onSave(editQuestion, index)} variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default QuestionnaireEditor;

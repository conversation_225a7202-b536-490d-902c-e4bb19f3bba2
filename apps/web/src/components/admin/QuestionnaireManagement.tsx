import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  LinearProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  Slider,
  TextField,
  DialogActions,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Edit,
  Settings,
  Timeline
} from '@mui/icons-material';
import axiosInstance from '../../services/axios';
import routes from '../../services/apiRoutes';
import { QuestionnaireType, QuestionnaireStats, SectionConfig } from '../../types/questionnaire-admin';
import QuestionnaireEditor from './QuestionnaireEditor';

interface QuestionnaireWithStats extends QuestionnaireType {
  stats: QuestionnaireStats;
  sections?: SectionConfig[];
}

const QuestionnaireManagement: React.FC = () => {
  const [questionnaires, setQuestionnaires] = useState<QuestionnaireWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [thresholdDialog, setThresholdDialog] = useState<{ open: boolean; questionnaire?: QuestionnaireWithStats }>({ open: false });
  const [newThreshold, setNewThreshold] = useState(0);
  const [editingQuestionnaire, setEditingQuestionnaire] = useState<string | null>(null);

  useEffect(() => {
    fetchQuestionnaires();
  }, []);

  const fetchQuestionnaires = async () => {
    try {
      const response = await axiosInstance.get(routes.GET_QUESTIONNAIRE_CONFIGS);
      setQuestionnaires(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch questionnaires:', error);
      setQuestionnaires([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const handleThresholdUpdate = async () => {
    if (!thresholdDialog.questionnaire) return;
    
    try {
      await axiosInstance.put(`${routes.UPDATE_QUESTIONNAIRE_THRESHOLD}/${thresholdDialog.questionnaire.id}/threshold`, {
        threshold: newThreshold
      });
      
      setQuestionnaires(prev => prev.map(q => 
        q.id === thresholdDialog.questionnaire?.id 
          ? { ...q, currentThreshold: newThreshold }
          : q
      ));
      
      setThresholdDialog({ open: false });
    } catch (error) {
      console.error('Failed to update threshold:', error);
    }
  };

  const openThresholdDialog = (questionnaire: QuestionnaireWithStats) => {
    setNewThreshold(Number(questionnaire.currentThreshold) || 0);
    setThresholdDialog({ open: true, questionnaire });
  };

  const handleToggleStatus = async (questionnaire: QuestionnaireWithStats) => {
    try {
      const newStatus = !questionnaire.isActive;
      await axiosInstance.put(`${routes.TOGGLE_QUESTIONNAIRE_STATUS}/${questionnaire.id}/status`, {
        isActive: newStatus
      });
      
      // Update local state
      setQuestionnaires(prev => prev.map(q => 
        q.id === questionnaire.id 
          ? { ...q, isActive: newStatus }
          : q
      ));
    } catch (error) {
      console.error('Failed to toggle questionnaire status:', error);
    }
  };

  if (loading) return <LinearProgress />;

  if (editingQuestionnaire) {
    return (
      <QuestionnaireEditor
        questionnaireId={editingQuestionnaire}
        onBack={() => setEditingQuestionnaire(null)}
      />
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>Messenger Questionnaire Management</Typography>

      {questionnaires.length === 0 && !loading ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No questionnaire configurations found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Please ensure the database migration has been run to create the questionnaire_configs table.
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {questionnaires.map((questionnaire) => (
          <Grid item xs={12} md={6} lg={3} key={questionnaire.id}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" component="div">{questionnaire.name}</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip 
                      label={questionnaire.isActive ? "Active" : "Inactive"} 
                      color={questionnaire.isActive ? "success" : "default"} 
                      size="small"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={questionnaire.isActive || false}
                          onChange={() => handleToggleStatus(questionnaire)}
                          size="small"
                        />
                      }
                      label=""
                    />
                  </Box>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Threshold: {questionnaire.currentThreshold || 0}/{questionnaire.maxScore || 0}
                    ({questionnaire.maxScore && questionnaire.currentThreshold !== undefined
                      ? Math.round((questionnaire.currentThreshold / questionnaire.maxScore) * 100)
                      : 0}%)
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={questionnaire.maxScore && questionnaire.currentThreshold !== undefined
                      ? (questionnaire.currentThreshold / questionnaire.maxScore) * 100
                      : 0}
                    sx={{ mt: 1 }}
                  />
                </Box>

                {/* Section Progress */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    <Timeline sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                    Sections: {questionnaire.sections?.length || 0} steps
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                    {questionnaire.sections?.slice(0, 8).map((section) => (
                      <Box
                        key={section.id}
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          backgroundColor: section.questions?.length > 0 ? 'success.main' : 'grey.300',
                          title: section.title
                        }}
                      />
                    ))}
                    {(questionnaire.sections?.length || 0) > 8 && (
                      <Typography variant="caption" color="text.secondary">
                        +{(questionnaire.sections?.length || 0) - 8}
                      </Typography>
                    )}
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Approval Rate: {isNaN(questionnaire.stats.approvalRate) ? '0.0' : questionnaire.stats.approvalRate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2">
                    Avg Score: {isNaN(questionnaire.stats.averageScore) ? '0.0' : questionnaire.stats.averageScore.toFixed(1)}
                  </Typography>
                  <Typography variant="body2">
                    Submissions (30d): {questionnaire.stats.totalSubmissions}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <IconButton
                    size="small"
                    onClick={() => setEditingQuestionnaire(questionnaire.id)}
                    title="Edit Questions"
                  >
                    <Settings />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => openThresholdDialog(questionnaire)}
                    title="Edit Threshold"
                  >
                    <Edit />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          ))}
        </Grid>
      )}

      <Dialog open={thresholdDialog.open} onClose={() => setThresholdDialog({ open: false })}>
        <DialogTitle>
          Update Threshold - {thresholdDialog.questionnaire?.name}
        </DialogTitle>
        <DialogContent sx={{ width: 400, pt: 2 }}>
          <Typography gutterBottom>
            Current: {thresholdDialog.questionnaire?.currentThreshold}/{thresholdDialog.questionnaire?.maxScore}
          </Typography>
          <Slider
            value={newThreshold}
            onChange={(_, value) => setNewThreshold(value as number)}
            min={0}
            max={thresholdDialog.questionnaire?.maxScore || 100}
            marks
            valueLabelDisplay="on"
            sx={{ mt: 2, mb: 2 }}
          />
          <TextField
            fullWidth
            type="number"
            label="Threshold"
            value={newThreshold}
            onChange={(e) => setNewThreshold(parseInt(e.target.value) || 0)}
            inputProps={{ min: 0, max: thresholdDialog.questionnaire?.maxScore }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setThresholdDialog({ open: false })}>Cancel</Button>
          <Button onClick={handleThresholdUpdate} variant="contained">Update</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default QuestionnaireManagement;

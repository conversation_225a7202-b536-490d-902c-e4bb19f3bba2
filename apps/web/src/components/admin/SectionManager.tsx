import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Text<PERSON>ield,
  IconButton,
  Chip,
  Alert,
  Grid,

} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { SectionConfig, QuestionConfig } from '../../types/questionnaire-admin';

interface SectionManagerProps {
  questionnaireId: string;
  sections: SectionConfig[];
  questions: QuestionConfig[];
  onSectionsChange: (sections: SectionConfig[]) => void;
  onSectionEdit: (sectionId: string) => void;
  onAddQuestionToSection: (sectionId: string) => void;
}

interface SectionEditDialog {
  open: boolean;
  section?: SectionConfig;
  isNew: boolean;
}

interface SectionStats {
  totalQuestions: number;
  maxScore: number;
}

const SectionManager: React.FC<SectionManagerProps> = ({
  sections,
  questions,
  onSectionsChange,
  onSectionEdit,
  onAddQuestionToSection
}) => {
  const [editDialog, setEditDialog] = useState<SectionEditDialog>({ open: false, isNew: false });
  const [sectionStats, setSectionStats] = useState<Record<string, SectionStats>>({});
  const [draggedSection, setDraggedSection] = useState<string | null>(null);
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{ open: boolean; sectionId: string | null; sectionTitle: string }>({
    open: false,
    sectionId: null,
    sectionTitle: ''
  });

  // Calculate section statistics
  useEffect(() => {
    const stats: Record<string, SectionStats> = {};
    sections.forEach(section => {
      const sectionQuestions = questions.filter(q => q.sectionId === section.id);
      const totalQuestions = sectionQuestions.length;
      const maxScore = sectionQuestions.reduce((sum, q) => sum + getQuestionMaxScore(q), 0);

      stats[section.id] = {
        totalQuestions,
        maxScore
      };
    });
    setSectionStats(stats);
  }, [sections, questions]);

  const getQuestionMaxScore = (question: QuestionConfig): number => {
    if (!question.contributesToScore) return 0;
    
    switch (question.type) {
      case 'radio':
      case 'dropdown':
        return Math.max(...(question.answerOptions?.map(opt => opt.score) || [0]));
      case 'checkbox':
        return (question.answerOptions?.reduce((sum, opt) => sum + opt.score, 0) || 0);
      case 'slider':
        return Math.max(...Object.values(question.sliderConfig?.scoreMapping || {}));
      case 'special_logic':
        return Math.max(...(question.specialLogic?.testCases?.map(tc => tc.expectedScore) || [0]));
      default:
        return 0;
    }
  };

  const handleSectionSave = () => {
    if (!editDialog.section) return;

    const updatedSections = editDialog.isNew
      ? [...sections, { ...editDialog.section, order: sections.length + 1 }]
      : sections.map(s => s.id === editDialog.section!.id ? editDialog.section! : s);

    onSectionsChange(updatedSections);
    setEditDialog({ open: false, isNew: false });
  };

  const handleSectionDelete = (sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (section) {
      const questionCount = questions.filter(q => q.sectionId === sectionId).length;
      setDeleteConfirmDialog({
        open: true,
        sectionId: sectionId,
        sectionTitle: `${section.title} (${questionCount} questions)`
      });
    }
  };

  const confirmSectionDelete = () => {
    if (deleteConfirmDialog.sectionId) {
      const updatedSections = sections.filter(s => s.id !== deleteConfirmDialog.sectionId);
      onSectionsChange(updatedSections);
      setDeleteConfirmDialog({ open: false, sectionId: null, sectionTitle: '' });
    }
  };

  const handleSectionReorder = (fromIndex: number, toIndex: number) => {
    const reorderedSections = [...sections];
    const [movedSection] = reorderedSections.splice(fromIndex, 1);
    reorderedSections.splice(toIndex, 0, movedSection);
    
    // Update order values
    const updatedSections = reorderedSections.map((section, index) => ({
      ...section,
      order: index + 1
    }));
    
    onSectionsChange(updatedSections);
  };

  const openEditDialog = (section?: SectionConfig) => {
    setEditDialog({
      open: true,
      section: section || {
        id: `new_section_${Date.now()}`,
        title: '',
        description: '',
        order: sections.length + 1,
        questions: [],
        validationRules: { requireAllQuestions: true },
        isActive: true
      },
      isNew: !section
    });
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">Section Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => openEditDialog()}
        >
          Add Section
        </Button>
      </Box>

      {/* Section Overview Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {sections.map((section, index) => {
          const stats = sectionStats[section.id] || { totalQuestions: 0, maxScore: 0 };
          
          return (
            <Grid item xs={12} md={6} lg={4} key={section.id}>
              <Card 
                sx={{ 
                  cursor: 'grab',
                  '&:hover': { boxShadow: 3 },
                  border: draggedSection === section.id ? '2px dashed #1976d2' : 'none'
                }}
                draggable
                onDragStart={() => setDraggedSection(section.id)}
                onDragEnd={() => setDraggedSection(null)}
                onDragOver={(e) => e.preventDefault()}
                onDrop={(e) => {
                  e.preventDefault();
                  if (draggedSection && draggedSection !== section.id) {
                    const fromIndex = sections.findIndex(s => s.id === draggedSection);
                    const toIndex = index;
                    handleSectionReorder(fromIndex, toIndex);
                  }
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <DragIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="h6" sx={{ flexGrow: 1 }}>
                      Step {section.order}: {section.title}
                    </Typography>
                    <IconButton size="small" onClick={() => openEditDialog(section)}>
                      <EditIcon />
                    </IconButton>
                  </Box>
                  
                  {section.description && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {section.description}
                    </Typography>
                  )}

                  <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                    <Chip 
                      label={`${stats.totalQuestions} Questions`} 
                      size="small" 
                      color="primary" 
                      variant="outlined" 
                    />
                    <Chip 
                      label={`${stats.maxScore} Points`} 
                      size="small" 
                      color="secondary" 
                      variant="outlined" 
                    />

                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={() => onAddQuestionToSection(section.id)}
                        variant="contained"
                        color="primary"
                      >
                        Add Question
                      </Button>
                      <Button
                        size="small"
                        startIcon={<SettingsIcon />}
                        onClick={() => onSectionEdit(section.id)}
                        variant="outlined"
                      >
                        Edit Questions
                      </Button>
                      <Button
                        size="small"
                        startIcon={<DeleteIcon />}
                        onClick={() => handleSectionDelete(section.id)}
                        variant="outlined"
                        color="error"
                        sx={{ ml: 1 }}
                      >
                        Delete
                      </Button>
                    </Box>

                  </Box>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Section Edit Dialog */}
      <Dialog 
        open={editDialog.open} 
        onClose={() => setEditDialog({ open: false, isNew: false })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editDialog.isNew ? 'Add New Section' : 'Edit Section'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Section Title"
              value={editDialog.section?.title || ''}
              onChange={(e) => setEditDialog(prev => ({
                ...prev,
                section: prev.section ? { ...prev.section, title: e.target.value } : undefined
              }))}
              sx={{ mb: 2 }}
            />
            
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={editDialog.section?.description || ''}
              onChange={(e) => setEditDialog(prev => ({
                ...prev,
                section: prev.section ? { ...prev.section, description: e.target.value } : undefined
              }))}
              sx={{ mb: 2 }}
            />

            <Typography variant="subtitle2" sx={{ mb: 1 }}>Validation Rules</Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              Section validation rules determine completion requirements and navigation logic.
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog({ open: false, isNew: false })}>
            Cancel
          </Button>
          <Button onClick={handleSectionSave} variant="contained">
            {editDialog.isNew ? 'Add Section' : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmDialog.open}
        onClose={() => setDeleteConfirmDialog({ open: false, sectionId: null, sectionTitle: '' })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Delete Section</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the section "{deleteConfirmDialog.sectionTitle}"?
          </Typography>
          <Alert severity="warning" sx={{ mt: 2 }}>
            This action cannot be undone. All questions in this section will remain but will need to be reassigned to other sections.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteConfirmDialog({ open: false, sectionId: null, sectionTitle: '' })}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmSectionDelete}
            variant="contained"
            color="error"
          >
            Delete Section
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SectionManager;

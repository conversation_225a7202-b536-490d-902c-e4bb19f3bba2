import React, { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Typography,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import { Download as DownloadIcon } from '@mui/icons-material';
import { DateTime } from 'luxon';
import adminRequestsService from '../../../services/admin-requests.service';

interface CSVExportComponentProps {
  onShowSnackbar?: (message: string, severity: "success" | "error" | "info") => void;
}

const CSVExportComponent: React.FC<CSVExportComponentProps> = ({ onShowSnackbar }) => {
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [questionnaireType, setQuestionnaireType] = useState<string>('');
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string>('');

  const questionnaireTypes = [
    { value: '', label: 'All Types' },
    { value: 'thc_increase', label: 'THC Increase (29% THC)' },
    { value: 'extend_tp', label: 'Extend Treatment Plan' },
    { value: 'add_22_thc', label: 'Add 22% THC' },
    { value: 'quantity_increase', label: 'Quantity Increase' }
  ];

  const handleExport = async () => {
    setError('');
    setIsExporting(true);

    try {
      // Validate date range
      if (startDate && endDate) {
        const start = DateTime.fromISO(startDate);
        const end = DateTime.fromISO(endDate);
        
        if (start > end) {
          setError('Start date must be before end date');
          setIsExporting(false);
          return;
        }
      }

      const filters: {
        startDate?: string;
        endDate?: string;
        questionnaireType?: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
      } = {};

      if (startDate) {
        filters.startDate = startDate;
      }
      if (endDate) {
        filters.endDate = endDate;
      }
      if (questionnaireType) {
        filters.questionnaireType = questionnaireType as 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
      }

      await adminRequestsService.exportRequestsToCSV(filters);
      
      onShowSnackbar?.('CSV export completed successfully', 'success');
    } catch (error) {
      console.error('Error exporting CSV:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to export CSV';
      setError(errorMessage);
      onShowSnackbar?.('Failed to export CSV', 'error');
    } finally {
      setIsExporting(false);
    }
  };

  const handleClearFilters = () => {
    setStartDate('');
    setEndDate('');
    setQuestionnaireType('');
    setError('');
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <DownloadIcon />
        Export Requests to CSV
      </Typography>
      
      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        Export request data including patient information, questionnaire responses, scores, and approval details.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
        <TextField
          label="Start Date"
          type="date"
          value={startDate}
          onChange={(e) => setStartDate(e.target.value)}
          InputLabelProps={{ shrink: true }}
          sx={{ minWidth: 200 }}
        />
        
        <TextField
          label="End Date"
          type="date"
          value={endDate}
          onChange={(e) => setEndDate(e.target.value)}
          InputLabelProps={{ shrink: true }}
          sx={{ minWidth: 200 }}
        />

        <FormControl sx={{ minWidth: 250 }}>
          <InputLabel>Questionnaire Type</InputLabel>
          <Select
            value={questionnaireType}
            label="Questionnaire Type"
            onChange={(e) => setQuestionnaireType(e.target.value)}
          >
            {questionnaireTypes.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                {type.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button
          variant="contained"
          startIcon={isExporting ? <CircularProgress size={20} /> : <DownloadIcon />}
          onClick={handleExport}
          disabled={isExporting}
          sx={{ minWidth: 140 }}
        >
          {isExporting ? 'Exporting...' : 'Export CSV'}
        </Button>

        <Button
          variant="outlined"
          onClick={handleClearFilters}
          disabled={isExporting}
        >
          Clear Filters
        </Button>
      </Box>

      <Typography variant="caption" color="textSecondary" sx={{ mt: 2, display: 'block' }}>
        <strong>Exported fields:</strong> Type, Patient ID, Email, Questionnaire Data, Strength Requests, 
        Total Score, Max Score, Is Eligible, Status, Approved By, Approved At, Approval Notes, Created At
      </Typography>
    </Paper>
  );
};

export default CSVExportComponent;

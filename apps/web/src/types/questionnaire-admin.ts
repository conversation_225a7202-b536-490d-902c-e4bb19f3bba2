export interface SectionValidationRules {
  requireAllQuestions: boolean;
  minimumQuestionsRequired?: number;
  customValidationLogic?: string;
}

export interface SectionConfig {
  id: string;
  title: string;
  description?: string;
  order: number;
  questions: QuestionConfig[];
  validationRules: SectionValidationRules;
  isActive: boolean;
}

export interface QuestionnaireConfig {
  id: string;
  name: string;
  maxScore: number;
  threshold: number;
  isActive: boolean;
  version: string;
  questions?: QuestionConfig[]; // Legacy support
  sections?: SectionConfig[]; // New section-based structure
  sectionsCount?: number;
  lastModified: Date;
  modifiedBy: string;
}

export interface SliderConfig {
  min: number;
  max: number;
  scoreMapping: Record<string, number>;
}

export interface TextFieldConfig {
  maxLength: number;
  required: boolean;
  placeholder: string;
  purpose: string;
}

export interface SpecialLogicConfig {
  questionKey: string;
  description: string;
  codeSnippet: string;
  testCases: Array<{
    input: string;
    expectedScore: number;
    description: string;
  }>;
  requiresDeveloper: boolean;
  lastModified: Date;
}

export interface DateConfig {
  minAge?: number;
  maxAge?: number;
  required: boolean;
}

export interface QuestionConfig {
  key: string;
  text: string;
  type: 'radio' | 'checkbox' | 'slider' | 'text' | 'special_logic' | 'date' | 'dropdown';
  sectionId?: string; // Links question to its section
  order: number;
  contributesToScore: boolean;
  isActive?: boolean;
  dependsOnQuestion?: string; // For conditional questions

  // Standard scoring (radio, checkbox, dropdown)
  answerOptions?: AnswerOption[];

  // Slider-specific
  sliderConfig?: SliderConfig;

  // Text field configuration
  textFieldConfig?: TextFieldConfig;

  // Special logic configuration
  specialLogic?: SpecialLogicConfig;

  // Date field configuration
  dateConfig?: DateConfig;
}

export interface AnswerOption {
  value: string;
  label: string;
  score: number;
}

export interface QuestionnaireStats {
  totalSubmissions: number;
  approvalRate: number;
  averageScore: number;
  scoreDistribution: { score: number; count: number }[];
}

export interface QuestionnaireType {
  id: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  name: string;
  tableName: string;
  maxScore: number;
  currentThreshold: number;
  isActive?: boolean;
}

export const QUESTIONNAIRE_TYPES: QuestionnaireType[] = [
  { id: 'thc_increase', name: 'THC Increase', tableName: 'thc_increase_questionnaire', maxScore: 61, currentThreshold: 45 },
  { id: 'extend_tp', name: 'Extend Treatment Plan', tableName: 'extend_tp_questionnaire', maxScore: 60, currentThreshold: 42 },
  { id: 'add_22_thc', name: 'Add 22% THC', tableName: 'add_22_thc_questionnaire', maxScore: 33, currentThreshold: 7 },
  { id: 'quantity_increase', name: 'Quantity Increase', tableName: 'quantity_increase_questionnaire', maxScore: 50, currentThreshold: 35 }
];

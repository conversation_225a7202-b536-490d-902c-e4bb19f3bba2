import express from 'express';
import { validateDr } from '../middlewares/validationMiddleware';
import {
  getAdminPendingRequests,
  getAdminProcessedRequests,
  getAdminRequestStats,
  getAdminPatientRequestHistory,
  getAdminNonEligibleRequests,
  exportAdminRequestsToCSV
} from '../controllers/admin/requests';

const router = express.Router();
const currentVersion = 'v1.0';

// All admin request routes require doctor/admin authentication
router.use(validateDr);

// Get pending requests for admin view
router.get(`/${currentVersion}/requests/pending`, getAdminPendingRequests);

// Get processed requests (approved/rejected) for admin view
router.get(`/${currentVersion}/requests/processed`, getAdminProcessedRequests);

// Get request statistics for admin dashboard
router.get(`/${currentVersion}/requests/stats`, getAdminRequestStats);

// Get non-eligible requests for admin view
router.get(`/${currentVersion}/requests/non-eligible`, getAdminNonEligibleRequests);

// Get patient request history for admin lookup
router.get(`/${currentVersion}/requests/patient/:id`, getAdminPatientRequestHistory);

// Export requests to CSV
router.get(`/${currentVersion}/requests/export/csv`, exportAdminRequestsToCSV);

export default router;

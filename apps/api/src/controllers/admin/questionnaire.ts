import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import httpStatus from 'http-status';
import { catchAll } from '../../utils/catchAll';
import { ApiResponse } from '../../helpers/response';
import { db } from '../../utils/db';

// TypeScript interfaces for questionnaire configuration
interface AnswerOption {
  value: string;
  label: string;
  score: number;
}

interface SliderConfig {
  min: number;
  max: number;
  scoreMapping: Record<string, number>;
}

interface TextFieldConfig {
  maxLength: number;
  required: boolean;
  placeholder: string;
  purpose: string;
}

interface SpecialLogicConfig {
  questionKey: string;
  description: string;
  codeSnippet: string;
  testCases: Array<{
    input: string;
    expectedScore: number;
    description: string;
  }>;
  requiresDeveloper: boolean;
  lastModified: Date;
}

interface DateConfig {
  minAge?: number;
  maxAge?: number;
  required: boolean;
}

interface QuestionConfig {
  key: string;
  text: string;
  type: 'radio' | 'checkbox' | 'slider' | 'text' | 'special_logic' | 'date' | 'dropdown';
  sectionId?: string; // Links question to its section
  order: number;
  contributesToScore: boolean;
  isActive?: boolean;
  dependsOnQuestion?: string; // For conditional questions

  // Standard scoring (radio, checkbox, dropdown)
  answerOptions?: AnswerOption[];

  // Slider-specific
  sliderConfig?: SliderConfig;

  // Text field configuration
  textFieldConfig?: TextFieldConfig;

  // Special logic configuration
  specialLogic?: SpecialLogicConfig;

  // Date field configuration
  dateConfig?: DateConfig;
}

// Section validation rules interface
interface SectionValidationRules {
  requireAllQuestions: boolean;
  minimumQuestionsRequired?: number;
  customValidationLogic?: string;
}

// Section configuration interface
interface SectionConfig {
  id: string;
  title: string;
  description?: string;
  order: number;
  questions: QuestionConfig[];
  validationRules: SectionValidationRules;
  isActive: boolean;
}



// Database interface (matches database schema)
interface QuestionnaireConfigDB {
  id: string;
  name: string;
  max_score: number;
  threshold: number;
  questions: QuestionConfig[]; // Legacy flat structure
  sections?: SectionConfig[]; // New section-based structure
  sections_count?: number;
  is_active: boolean;
  version?: string;
  last_modified: Date;
  modified_by: string;
  created_at?: Date;
}

// Frontend interface (camelCase for frontend)
interface QuestionnaireConfig {
  id: string;
  name: string;
  maxScore: number;
  threshold: number;
  questions?: QuestionConfig[]; // Legacy support
  sections?: SectionConfig[]; // New section-based structure
  sectionsCount?: number;
  is_active: boolean;
  version?: string;
  last_modified: Date;
  modified_by: string;
  created_at?: Date;
}

/**
 * Get all questionnaire configurations and stats
 */
export const getQuestionnaireConfigs: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();
  try {
    // Get configs from database
    const configQuery = `
      SELECT id, name, max_score, threshold, is_active, version, questions, last_modified, modified_by
      FROM questionnaire_configs
      ORDER BY name
    `;
    const configResult = await client.query(configQuery);

    const tableMap: Record<string, string> = {
      'thc_increase': 'thc_increase_questionnaire',
      'extend_tp': 'extend_tp_questionnaire',
      'add_22_thc': 'add_22_thc_questionnaire',
      'quantity_increase': 'quantity_increase_questionnaire'
    };

    const results = await Promise.all(configResult.rows.map(async (config: QuestionnaireConfigDB) => {
      const tableName = tableMap[config.id];
      if (!tableName) return { ...config, stats: { totalSubmissions: 0, approvalRate: 0, averageScore: 0 } };

      // Get sections for this questionnaire
      const sectionsQuery = `
        SELECT id, title, description, order_index as "order", validation_rules, is_active
        FROM questionnaire_sections
        WHERE questionnaire_id = $1 AND is_active = true
        ORDER BY order_index
      `;
      const sectionsResult = await client.query(sectionsQuery, [config.id]);

      const sections: SectionConfig[] = sectionsResult.rows.map(row => ({
        id: row.id,
        title: row.title,
        description: row.description,
        order: row.order,
        questions: [], // Questions will be populated from the main config if needed
        validationRules: row.validation_rules || { requireAllQuestions: true },
        isActive: row.is_active
      }));

      const statsQuery = `
        SELECT
          COUNT(*) as total_submissions,
          AVG(total_score) as average_score,
          CASE
            WHEN COUNT(*) = 0 THEN 0
            ELSE COUNT(CASE WHEN is_eligible = true THEN 1 END)::float / COUNT(*) * 100
          END as approval_rate
        FROM ${tableName}
        WHERE created_at >= NOW() - INTERVAL '30 days'
      `;
      const stats = await client.query(statsQuery);

      // Convert database fields to expected format
      const formattedConfig = {
        ...config,
        maxScore: Number(config.max_score) || 0,
        currentThreshold: Number(config.threshold) || 0,
        isActive: config.is_active,
        tableName,
        sections: sections.length > 0 ? sections : undefined,
        stats: {
          totalSubmissions: parseInt(stats.rows[0].total_submissions) || 0,
          approvalRate: parseFloat(stats.rows[0].approval_rate) || 0,
          averageScore: parseFloat(stats.rows[0].average_score) || 0
        }
      };
      return formattedConfig;
    }));

    res.status(httpStatus.OK).json(new ApiResponse(httpStatus.OK, 'SUCCESS', results, true));
  } finally {
    client.release();
  }
});

/**
 * Get specific questionnaire configuration
 */
export const getQuestionnaireConfig: RequestHandler = catchAll(async (req, res) => {
  const { questionnaireId } = req.params;
  const client = await db.connect();

  try {
    // Get main questionnaire config
    const configQuery = `
      SELECT id, name, max_score, threshold, is_active, version, questions, last_modified, modified_by, sections_count
      FROM questionnaire_configs
      WHERE id = $1
    `;
    const configResult = await client.query(configQuery, [questionnaireId]);

    if (configResult.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).json(
        new ApiResponse(httpStatus.NOT_FOUND, 'Questionnaire not found', null, false)
      );
      return;
    }

    // Get sections if they exist
    const sectionsQuery = `
      SELECT id, title, description, order_index as "order", validation_rules, is_active
      FROM questionnaire_sections
      WHERE questionnaire_id = $1 AND is_active = true
      ORDER BY order_index
    `;
    const sectionsResult = await client.query(sectionsQuery, [questionnaireId]);

    const sections: SectionConfig[] = sectionsResult.rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      order: row.order,
      questions: [], // Questions will be populated from the main config
      validationRules: row.validation_rules || { requireAllQuestions: true },
      isActive: row.is_active
    }));

    const rawConfig: QuestionnaireConfigDB = configResult.rows[0];
    const config: QuestionnaireConfig = {
      ...rawConfig,
      maxScore: Number(rawConfig.max_score) || 0,
      threshold: Number(rawConfig.threshold) || 0,
      sections: sections.length > 0 ? sections : undefined,
      sectionsCount: Number(rawConfig.sections_count) || 0
    };

    res.status(httpStatus.OK).json(new ApiResponse(httpStatus.OK, 'SUCCESS', config, true));
  } finally {
    client.release();
  }
});

/**
 * Update questionnaire threshold
 */
export const updateQuestionnaireThreshold: RequestHandler = catchAll(async (req, res) => {
  const { questionnaireId } = req.params;
  const { threshold }: { threshold: number } = req.body;
  const client = await db.connect();

  try {
    const query = `
      UPDATE questionnaire_configs
      SET threshold = $1, last_modified = NOW(), modified_by = $2
      WHERE id = $3
      RETURNING id, threshold
    `;
    const result = await client.query(query, [threshold, 'admin', questionnaireId]);

    if (result.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).json(
        new ApiResponse(httpStatus.NOT_FOUND, 'Questionnaire not found', null, false)
      );
      return;
    }

    const updatedThreshold: { id: string; threshold: number } = result.rows[0];
    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'Threshold updated successfully', updatedThreshold, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Update questionnaire configuration
 */
export const updateQuestionnaireConfig: RequestHandler = catchAll(async (req, res) => {
  const { questionnaireId } = req.params;
  const { name, maxScore, threshold, questions, sections }: {
    name: string;
    maxScore: number;
    threshold: number;
    questions?: QuestionConfig[];
    sections?: SectionConfig[];
  } = req.body;
  const client = await db.connect();

  try {
    // Update main questionnaire config
    const query = `
      UPDATE questionnaire_configs
      SET name = $1, max_score = $2, threshold = $3, questions = $4,
          last_modified = NOW(), modified_by = $5, version = version::float + 0.1
      WHERE id = $6
      RETURNING *
    `;
    const result = await client.query(query, [name, maxScore, threshold, JSON.stringify(questions || []), 'admin', questionnaireId]);

    if (result.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).json(
        new ApiResponse(httpStatus.NOT_FOUND, 'Questionnaire not found', null, false)
      );
      return;
    }

    // If sections are provided, update them
    if (sections && Array.isArray(sections)) {
      // Delete existing sections
      await client.query('DELETE FROM questionnaire_sections WHERE questionnaire_id = $1', [questionnaireId]);

      // Insert new sections
      for (const section of sections) {
        await client.query(`
          INSERT INTO questionnaire_sections (questionnaire_id, title, description, order_index, validation_rules)
          VALUES ($1, $2, $3, $4, $5)
        `, [questionnaireId, section.title, section.description, section.order, JSON.stringify(section.validationRules)]);
      }
    }

    const updatedConfig: QuestionnaireConfig = result.rows[0];
    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'Configuration updated successfully', updatedConfig, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get sections for a questionnaire
 */
export const getQuestionnaireSections: RequestHandler = catchAll(async (req, res) => {
  const { questionnaireId } = req.params;
  const client = await db.connect();

  try {
    const query = `
      SELECT id, title, description, order_index as "order", validation_rules, is_active
      FROM questionnaire_sections
      WHERE questionnaire_id = $1 AND is_active = true
      ORDER BY order_index
    `;
    const result = await client.query(query, [questionnaireId]);

    const sections: SectionConfig[] = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      order: row.order,
      questions: [], // Questions will be populated separately
      validationRules: row.validation_rules || { requireAllQuestions: true },
      isActive: row.is_active
    }));

    res.status(httpStatus.OK).json(new ApiResponse(httpStatus.OK, 'SUCCESS', sections, true));
  } finally {
    client.release();
  }
});

/**
 * Update section order
 */
export const updateSectionOrder: RequestHandler = catchAll(async (req, res) => {
  const { questionnaireId } = req.params;
  const { sections }: { sections: Array<{ id: string; order: number }> } = req.body;
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    for (const section of sections) {
      await client.query(`
        UPDATE questionnaire_sections
        SET order_index = $1, last_modified = NOW()
        WHERE id = $2 AND questionnaire_id = $3
      `, [section.order, section.id, questionnaireId]);
    }

    await client.query('COMMIT');
    res.status(httpStatus.OK).json(new ApiResponse(httpStatus.OK, 'Section order updated successfully', null, true));
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
});

/**
 * Toggle questionnaire active status
 */
export const toggleQuestionnaireStatus: RequestHandler = catchAll(async (req, res) => {
  const { questionnaireId } = req.params;
  const { isActive }: { isActive: boolean } = req.body;
  const client = await db.connect();

  try {
    const query = `
      UPDATE questionnaire_configs
      SET is_active = $1, last_modified = NOW(), modified_by = $2
      WHERE id = $3
      RETURNING id, name, is_active, last_modified, modified_by
    `;
    const result = await client.query(query, [isActive, 'admin', questionnaireId]);

    if (result.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).json(
        new ApiResponse(httpStatus.NOT_FOUND, 'Questionnaire not found', null, false)
      );
      return;
    }

    const updatedQuestionnaire = result.rows[0];
    res.status(httpStatus.OK).json(
      new ApiResponse(
        httpStatus.OK, 
        `Questionnaire ${isActive ? 'activated' : 'deactivated'} successfully`, 
        updatedQuestionnaire, 
        true
      )
    );
  } finally {
    client.release();
  }
});

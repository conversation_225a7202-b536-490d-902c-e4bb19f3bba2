# Inventory Report System Documentation

## Overview

The Inventory Report System is a comprehensive daily inventory tracking tool designed to monitor and manage inventory levels for medical cannabis products. The system uses **daily snapshots** to provide accurate historical inventory data, tracking opening balances, quantities dispensed, and closing balances for each product on a daily basis.

### Key Features

- **Daily Snapshot System**: Creates historical snapshots of inventory data for each day
- **Automated Backfill**: Automatically backfills missing historical data
- **Professional Reports**: Generates clean, professional inventory reports as images
- **Slack Integration**: Automatically sends daily reports to designated Slack channels
- **Real-time Data**: Uses live order data from WordPress and current stock balances
- **Timezone Aware**: All operations use Australia/Sydney timezone for consistency

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Daily Snapshot System](#daily-snapshot-system)
3. [Data Sources](#data-sources)
4. [Data Structures](#data-structures)
5. [Processing Logic](#processing-logic)
6. [Report Generation](#report-generation)
7. [API Endpoints](#api-endpoints)
8. [Configuration](#configuration)
9. [Database Schema](#database-schema)
10. [Dependencies](#dependencies)

## Architecture Overview

The Inventory Report System is built around a **daily snapshot architecture** that ensures data consistency and historical accuracy:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   WordPress     │    │   ProductStock   │    │ Daily Snapshots │
│   Order Data    │───▶│   Current        │───▶│   Historical    │
│   (Live)        │    │   Balances       │    │   Data          │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Daily Report    │    │   Slack         │
                       │  Generation      │───▶│   Notification  │
                       │  (HTML → PNG)    │    │   & S3 Storage  │
                       └──────────────────┘    └─────────────────┘
```

## Daily Snapshot System

The system creates daily snapshots that capture the complete inventory state for each day. This approach provides:

### Benefits
- **Historical Accuracy**: Each day's data is preserved exactly as it was
- **Audit Trail**: Complete history of inventory changes over time
- **Data Consistency**: Reports always show the same data for a given date
- **Performance**: Fast report generation using pre-calculated snapshots

### Snapshot Creation Process
1. **Trigger**: Snapshots are created when generating reports for dates that don't have existing snapshots
2. **Data Collection**: Gathers orders for the specific date and current product balances
3. **Calculation**: Computes opening balance, quantity dispensed, and closing balance
4. **Storage**: Saves snapshot data to `daily_inventory_snapshots` table
5. **Validation**: Ensures mathematical accuracy (Opening - Dispensed = Closing)

## Data Sources

### Primary Data Sources

The inventory report system relies on multiple data sources to generate comprehensive inventory tracking:

#### 1. Order Data (`/sync/v1/orderdata`)
- **Source:** WordPress site at `letsroll.harvest.delivery`
- **Endpoint:** `{WP_API_URL}/sync/v1/orderdata?start_date={start}&end_date={end}&status=paid`
- **Authentication:** JWT token-based authentication
- **Data Type:** JSON response containing order information
- **Key Fields:**
  - `order_number`
  - `consulting_doctor`
  - `order_items` (array containing):
    - `trade_name`
    - `product_name`
    - `quantity`

#### 2. Product Stock Database (`productstock` table)
- **Source:** Local PostgreSQL database
- **Table:** `productstock`
- **Purpose:** Stores current inventory balances for all products
- **Key Fields:**
  - `id` (UUID)
  - `name` (product name)
  - `code` (product code/trade name)
  - `balance` (current stock quantity)

#### 3. Daily Inventory Snapshots (`daily_inventory_snapshots` table)
- **Source:** Local PostgreSQL database
- **Table:** `daily_inventory_snapshots`
- **Purpose:** Stores daily historical inventory snapshots for accurate reporting
- **Key Fields:**
  - `report_date` (date of the snapshot)
  - `product_name` (friendly product name)
  - `sku` (product code/trade name)
  - `strength` (THC percentage)
  - `opening_balance` (balance at start of day)
  - `quantity_dispensed` (amount dispensed during the day)
  - `closing_balance` (balance at end of day)
  - `cumulative_dispensed` (total dispensed to date)

### Data Source Configuration

```typescript
// Environment variables required
WP_API_URL: string          // WordPress API base URL
WP_USERNAME: string         // WordPress username for authentication
WP_PASSWORD: string         // WordPress password for authentication
```

## Data Structures

### Core Data Types

#### Orders
Represents order data structure from WordPress API:

```typescript
export type Orders = {
  consulting_doctor: string;
  order_number: string;
  order_items: {
    trade_name: string;
    product_name: string;
    quantity: number;
  }[];
};
```

#### CurrentBalance
Represents current inventory balance for a product:

```typescript
export type CurrentBalance = {
  id: string;
  name: string;
  code: string;
  balance: number;
};
```

#### SupplyRequest
Represents aggregated supply requests by doctor:

```typescript
type SupplyRequest = {
  [doctorName: string]: {
    products: {
      [productName: string]: {
        baseName: string;
        totalQuantity: number;
        totalWeight: number;
        totalBudQuantity: number;
        tradeName: string;
        orderNumber: string;
      };
    };
  };
};
```

#### ProductCentricSupply
Represents product-centric inventory data:

```typescript
interface ProductCentricSupply {
  [productName: string]: {
    requests: ProductRequest[];
    totalBudQuantity: number;
    tradeName: string;
    strength: string;
    balance: number;
    orderNumber: string;
  };
}
```

## Processing Logic

### Daily Snapshot Creation

The system creates daily snapshots using the following process:

```typescript
async createDailySnapshotForce(date: DateTime): Promise<void> {
  const dateStr = date.toFormat('yyyy-MM-dd');

  // 1. Get orders for the specific date
  const startOfDay = date.startOf('day');
  const endOfDay = date.endOf('day');
  const ordersForDate = await WordPressUtils.getOrders(
    startOfDay.toFormat('yyyy-MM-dd'),
    endOfDay.toFormat('yyyy-MM-dd'),
    'paid'
  );

  // 2. Calculate dispensed quantities for the date
  const dailyQuantities = this.calculateTodayDispensedQuantities(ordersForDate.data);

  // 3. Get current product balances
  const currentBalances = await client.query(`SELECT * FROM productstock`);

  // 4. Create snapshot entries
  for (const currentBalance of currentBalances.rows) {
    const dailyDispensed = dailyQuantities.get(currentBalance.code) || 0;

    snapshotEntries.push({
      reportDate: dateStr,
      productName: currentBalance.name,
      sku: currentBalance.code,
      strength: getStrengthFromTradeNameMapping(currentBalance.code),
      openingBalance: currentBalance.balance + dailyDispensed, // Backtrack to opening
      quantityDispensed: dailyDispensed,
      closingBalance: currentBalance.balance, // Current balance
      cumulativeDispensed: dailyDispensed,
    });
  }

  // 5. Insert snapshots into database
  await client.query(insertQuery, insertValues);
}
```

### Snapshot-Based Reporting

Reports are generated using pre-calculated snapshots:

```typescript
async generateDailyInventoryReportData(): Promise<InventoryReportData> {
  const sydneyTime = DateTime.now().setZone('Australia/Sydney');
  const reportDate = sydneyTime.minus({ days: 1 }).toFormat('yyyy-MM-dd');
  const yesterdayDate = sydneyTime.minus({ days: 1 });

  // Ensure snapshot exists for the report date
  await this.ensureDailySnapshotExists(yesterdayDate);

  // Get snapshot data
  const yesterdayData = await this.getYesterdayIncrementalData(sydneyTime);

  // Use snapshot data directly (no recalculation needed)
  const skuDetails = yesterdayData.yesterdaySnapshots.map(snapshot => ({
    sku: snapshot.sku,
    productName: snapshot.product_name,
    strength: snapshot.strength,
    openingBalance: Number(snapshot.opening_balance),
    quantityDispensed: Number(snapshot.quantity_dispensed),
    closingBalance: Number(snapshot.closing_balance),
    unit: 'g',
    lastUpdated: sydneyTime.toISO()
  }));

  return { reportDate, skuDetails, /* ... */ };
}
```

### Order Filtering and Deduplication

```typescript
const oldOrders = result.rows[0]?.orders || { data: [] };
const orders = await WordPressUtils.getOrders('2025-06-08', nextDay.toFormat('yyyy-MM-dd'), 'paid');
const filteredOrders = orders.data.filter((d) => 
  !oldOrders.data.find((o) => o.order_number === d.order_number)
);
```

## Inventory Calculation Algorithm

### Doctor Supply Request Generation

The `generateDoctorSupplyRequests` function aggregates orders by doctor and product:

```typescript
export const generateDoctorSupplyRequests = (orders: Orders[]): SupplyRequest => {
  const doctorSupply = {};

  // Process each doctor's orders
  for (const order of orders) {
    const doctor = order.consulting_doctor;

    // Initialize doctor if not exists
    if (!doctorSupply[doctor]) {
      doctorSupply[doctor] = { products: {} };
    }

    // Process each product in the order
    for (const item of order.order_items) {
      // Extract base product name (before first hyphen)
      const productKey = item.product_name.split('-')[0].trim();

      // Extract weight (assuming format "XXg")
      const weightMatch = item.product_name.match(/(\d+\.?\d*)g/);
      const weight = weightMatch ? parseFloat(weightMatch[1]) : 0;

      // Format trade dates (remove brackets and replace | with /)
      if (item.trade_name) {
        const formattedTradeDates = item.trade_name?.replace(/[[\]]/g, '').replace(/\|/g, '/');

        if (formattedTradeDates) {
          if (!doctorSupply[doctor].products[productKey]) {
            doctorSupply[doctor].products[productKey] = {
              baseName: productKey,
              totalQuantity: 0,
              totalWeight: 0,
              totalBudQuantity: 0,
              tradeName: formattedTradeDates,
              orderNumber: order.order_number,
            };
          }

          // Update product aggregates
          const product = doctorSupply[doctor].products[productKey];
          product.totalQuantity += item.quantity;
          product.totalWeight += weight * item.quantity;
          product.totalBudQuantity = product.totalWeight / 3.5; // Convert to bud quantity
        }
      }
    }
  }

  return doctorSupply;
};
```

### Product-Centric Supply Calculation

The `generateProductCentricSupply` function creates product-centric inventory data:

```typescript
export const generateProductCentricSupply = (
  doctorSupply: ReturnType<typeof generateDoctorSupplyRequests>,
  balance: CurrentBalance[],
  newOrders: Orders[],
): ProductCentricSupply => {
  const productCentricSupply: ProductCentricSupply = {};

  // First pass: Collect all product data from doctors
  for (const [drName, doctorData] of Object.entries(doctorSupply)) {
    for (const [productName, productData] of Object.entries(doctorData.products)) {
      if (!productCentricSupply[productName]) {
        productCentricSupply[productName] = {
          requests: [],
          totalBudQuantity: 0,
          tradeName: productData.tradeName,
          strength: tradeNameMapping[productData.tradeName]?.strength || 'Unknown',
          balance: balance.find((b) => b.code === productData.tradeName)?.balance || 0,
          orderNumber: productData.orderNumber,
        };
      }

      // Add this doctor's request
      productCentricSupply[productName].requests.push({
        drName,
        budQuantity: productData.totalBudQuantity,
      });

      // Update total
      productCentricSupply[productName].totalBudQuantity += productData.totalBudQuantity;
    }
  }

  // Second pass: Calculate remaining balance
  for (const productName in productCentricSupply) {
    const isNewOrder = newOrders.find((n) => 
      n.order_number === productCentricSupply[productName].orderNumber
    );
    if (isNewOrder) {
      productCentricSupply[productName].balance -= productCentricSupply[productName].totalBudQuantity;
    }
  }

  return productCentricSupply;
};
```

### Inventory Balance Updates

The system updates product balances in the database:

```typescript
const updateBalanceQuery = (valuesClause: string) => `
  UPDATE productstock
  SET balance = balance - updates.total_bud_quantity
  FROM (VALUES ${valuesClause}) AS updates(code, total_bud_quantity)
  WHERE productstock.code = updates.code;
`;

// Update balances for new orders
if (copyOfFilteredOrders.length > 0) {
  const newOuput = generateDoctorSupplyRequests(copyOfFilteredOrders);
  const newProductCentricData = generateProductCentricSupply(
    newOuput,
    currentBalance.rows as CurrentBalance[],
    copyOfFilteredOrders,
  );
  const valuesClause = Object.entries(newProductCentricData)
    .map(([, data]) => `('${data.tradeName.replace(/'/g, "''")}', ${data.totalBudQuantity})`)
    .join(', ');
  await client.query(updateBalanceQuery(valuesClause));
}
```

## Report Generation

### Key Metrics Calculated

1. **Opening Balance:** Current stock level from `productstock` table
2. **Quantity Dispensed:** Total quantity ordered by doctors
3. **Closing Balance:** Opening balance minus quantity dispensed
4. **Product SKU:** Product code/trade name
5. **Date:** Report generation date

### Report Structure

The system generates a comprehensive inventory report with:

```typescript
res.status(200).send({
  output,                    // Doctor supply requests
  productCentricData,        // Product-centric inventory data
  orders,                    // All processed orders
  filteredOrders,           // New orders since last pull
});
```

### Report Components

1. **Doctor Supply Requests:** Aggregated orders by doctor
2. **Product-Centric Data:** Inventory data organized by product
3. **Order History:** Complete order processing history
4. **Balance Updates:** Real-time inventory balance calculations

## API Endpoints

### Daily Inventory Report Endpoints

#### Generate Daily Report as Image
```typescript
POST /api/inventory/v1/trigger-daily-report-image
```

**Description:** Generates a daily inventory report as a PNG image and sends it to Slack.

**Response:**
```typescript
{
  success: boolean;
  message: string;
  timestamp: string;
  reportType: "daily-image";
}
```

#### Get Daily Report Data (JSON)
```typescript
GET /api/inventory/v1/daily-report-data
```

**Description:** Returns the daily inventory report data as JSON.

**Response:**
```typescript
{
  reportDate: string;           // Date of the report (YYYY-MM-DD)
  reportTime: string;           // Time when report was generated
  totalSKUs: number;            // Total number of products
  totalOpeningBalance: number;  // Sum of all opening balances
  totalQuantityDispensed: number; // Sum of all dispensed quantities
  totalClosingBalance: number;  // Sum of all closing balances
  skuDetails: SKUDetail[];      // Detailed data for each product
  metadata: {
    lastDataPull: string;       // Timestamp of data collection
    ordersProcessed: number;    // Number of orders processed
    newOrdersCount: number;     // Number of new orders
  };
}
```

#### Get Daily Report as HTML
```typescript
GET /api/inventory/v1/daily-report-image
```

**Description:** Returns the daily inventory report as an HTML page for browser viewing.

### Snapshot Management Endpoints

#### Backfill Yesterday's Snapshot
```typescript
POST /api/inventory/v1/backfill-yesterday
```

**Description:** Creates a snapshot for yesterday's data (non-destructive - won't overwrite existing snapshots).

**Response:**
```typescript
{
  success: boolean;
  message: string;
  date: string; // Date that was backfilled
}
```

#### Force Backfill Yesterday's Snapshot
```typescript
POST /api/inventory/v1/force-backfill-yesterday
```

**Description:** Forces creation of yesterday's snapshot, deleting any existing data for that date.

**Response:**
```typescript
{
  success: boolean;
  message: string;
  date: string; // Date that was backfilled
}
```

### Legacy Medicine Register Endpoint

#### Medicine Register Data
```typescript
POST /api/report/v1.0/medicine-register
```

**Request Body:**
```typescript
{
  pin: string;              // PIN for accessing inventory data
}
```

**Response:**
```typescript
{
  found: boolean;           // Whether PIN is valid
}
```

## Configuration

### Environment Variables

```bash
# WordPress API Configuration
WP_API_URL=https://letsroll.harvest.delivery/wp-json
WP_USERNAME=your_username
WP_PASSWORD=your_password

# Database Configuration
DB_HOST=localhost
DB_NAME=docui
DB_USER=docui
DB_PASSWORD=docui
```

### Date Configuration

- **Order Date Range:** From '2025-06-08' to current date
- **Cache Duration:** 4 hours (configurable)
- **Report Frequency:** Daily (as per specification)

## Database Schema

### Daily Inventory Snapshots Table (Primary)

```sql
CREATE TABLE daily_inventory_snapshots (
  id SERIAL PRIMARY KEY,
  report_date DATE NOT NULL,
  product_name VARCHAR(255) NOT NULL,
  sku VARCHAR(255) NOT NULL,
  strength VARCHAR(255),
  opening_balance DECIMAL(10,2) NOT NULL,
  quantity_dispensed DECIMAL(10,2) NOT NULL,
  closing_balance DECIMAL(10,2) NOT NULL,
  cumulative_dispensed DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(report_date, sku)
);

CREATE INDEX idx_daily_snapshots_date ON daily_inventory_snapshots(report_date);
CREATE INDEX idx_daily_snapshots_sku ON daily_inventory_snapshots(sku);
```

**Key Features:**
- **Unique Constraint**: Prevents duplicate snapshots for the same date/product
- **Date Indexing**: Fast queries by report date
- **SKU Indexing**: Fast queries by product
- **Decimal Precision**: Accurate balance calculations
- **Audit Trail**: Created timestamp for tracking

### Product Stock Table (Reference Data)

```sql
CREATE TABLE "public"."productstock" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" text NOT NULL,
  "code" text NOT NULL,
  "balance" integer NOT NULL
);
```

**Purpose:** Stores current inventory balances used for snapshot calculations.

### Medicine Register PIN Table

```sql
CREATE TABLE medicineregisterpin (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  pin TEXT NOT NULL,
  active BOOLEAN DEFAULT TRUE
);
```

## Dependencies

### Core Dependencies

```json
{
  "luxon": "^3.0.0",           // Date/time manipulation
  "express": "^4.18.0",        // Web framework
  "axios": "^1.0.0",           // HTTP client for WordPress API
  "pg": "^8.0.0"              // PostgreSQL client
}
```

### External Services

1. **WordPress Site:** `letsroll.harvest.delivery`
   - Custom REST API endpoints
   - JWT authentication
   - Order data

2. **PostgreSQL Database:** Local data storage
   - Product stock balances
   - Medicine register management
   - PIN verification

## Data Flow Summary

1. **Data Retrieval:** Fetch orders from WordPress API
2. **Caching Check:** Verify if recent data exists (within 4 hours)
3. **Order Processing:** Filter new orders and aggregate by doctor/product
4. **Inventory Calculation:** Calculate opening/closing balances
5. **Balance Updates:** Update product stock balances in database
6. **Data Storage:** Store processed data in medicine register management table
7. **Report Generation:** Return comprehensive inventory report

## Business Logic

### Inventory Management Lifecycle

1. **Opening Balance:** Current stock level from database
2. **Order Processing:** Aggregate orders by doctor and product
3. **Quantity Calculation:** Calculate total quantity dispensed
4. **Balance Update:** Subtract dispensed quantity from opening balance
5. **Closing Balance:** Updated inventory level after orders

### Key Features

1. **Duplicate Prevention:** Tracks processed orders to avoid double-counting
2. **Caching:** Returns cached data if recent (within 4 hours)
3. **Real-time Updates:** Updates product balances as orders are processed
4. **PIN Protection:** Requires PIN verification for access
5. **Product Mapping:** Maps trade names to product codes and strengths

### Inventory Metrics

- **Date:** Report generation date
- **SKU:** Product stock keeping unit (trade name)
- **Opening Balance:** Starting inventory level
- **Quantity Dispensed:** Total quantity ordered
- **Closing Balance:** Ending inventory level

This system provides comprehensive inventory tracking and management for medical cannabis products, ensuring accurate stock levels and efficient supply chain management. 
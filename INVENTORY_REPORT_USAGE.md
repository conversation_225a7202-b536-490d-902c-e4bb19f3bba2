# Inventory Report System - Usage Guide

## Overview

The Inventory Report System generates daily inventory reports using a **daily snapshot architecture**. The system creates historical snapshots of inventory data and generates professional reports as images that are automatically sent to Slack channels.

### Key Features
- **Daily Snapshots**: Historical inventory data preserved for each day
- **Automated Backfill**: Missing historical data is automatically created
- **Professional Reports**: Clean, formatted reports without technical SKU codes
- **Timezone Aware**: All operations use Australia/Sydney timezone
- **Mathematical Accuracy**: Opening - Dispensed = Closing balance validation

## Environment Variables

Add these environment variables to your `.env` file:

```bash
# Inventory Reporting Configuration
INVENTORY_REPORTING_ENABLED=true
INVENTORY_DATA_RETENTION_DAYS=365
INVENTORY_START_DATE=2025-06-08
SLACK_INVENTORY_REPORT_CHANNEL=#inventory-reports

# Required for image generation and S3 storage
AWS_REGION=ap-southeast-2
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_BUCKET_NAME=your_bucket_name

# Required for Slack integration
SLACK_TOKEN=your_slack_bot_token
```

### Important Configuration Notes

- **INVENTORY_START_DATE**: This is the date when inventory tracking began in the system (default: 2025-06-08). The system will only process orders from this date forward to maintain data consistency and avoid processing historical data that predates the inventory system implementation.

## Quick Setup for Daily Snapshot Reports

The system automatically creates snapshots as needed, but you can manually manage them:

### Generate Daily Report (Automatic Snapshot Creation)
```bash
# Generate today's report (creates snapshots automatically if missing)
curl -X POST http://localhost:5000/api/inventory/v1/trigger-daily-report-image
```

### Manual Snapshot Management
```bash
# Backfill yesterday's snapshot (safe - won't overwrite existing)
curl -X POST http://localhost:5000/api/inventory/v1/backfill-yesterday

# Force backfill yesterday's snapshot (destructive - overwrites existing)
curl -X POST http://localhost:5000/api/inventory/v1/force-backfill-yesterday

# View report data as JSON
curl -X GET http://localhost:5000/api/inventory/v1/daily-report-data

# View report as HTML in browser
open http://localhost:5000/api/inventory/v1/daily-report-image
```

### How Daily Snapshots Work
- **Report Date**: Shows yesterday's activity (the completed day)
- **Opening Balance**: What was available at start of the day
- **Quantity Dispensed**: What was actually dispensed during that day
- **Closing Balance**: What remained at end of the day
- **Mathematical Validation**: Opening - Dispensed = Closing (always accurate)

### Daily Cutoff Time
The system uses **midnight AEST** as the daily cutoff:
- Orders placed before 12:00:00 AM AEST belong to the previous day
- Orders placed at or after 12:00:00 AM AEST belong to the current day
- Reports generated at midnight will include all orders up to 11:59:59 PM AEST

## API Endpoints

### Core Report Endpoints

#### Trigger Daily Inventory Report (Image)
```bash
POST /api/inventory/v1/trigger-daily-report-image
```
Generates and sends the daily inventory report as an image to Slack.

#### Trigger Daily Inventory Report (Text)
```bash
POST /api/inventory/v1/trigger-daily-report
```
Currently redirects to the image version. Can be extended for text-based reports.

### Testing Endpoints

#### Test Report
```bash
POST /api/inventory/v1/test-report
```
Sends a test inventory report to Slack with current data.

#### Generate Test Data
```bash
POST /api/inventory/v1/generate-test-data
```
Returns system status and available endpoints for testing.

#### Backfill Yesterday's Snapshot
```bash
POST /api/inventory/v1/backfill-yesterday
```
Creates a daily snapshot for yesterday to enable proper incremental reporting. **Run this once before generating your first daily report.**

### Data Access Endpoints

#### Get Inventory Data
```bash
GET /api/inventory/v1/data?startDate=2024-01-01&endDate=2024-01-31
```
Retrieves inventory data for a specific date range.

### Monitoring Endpoints

#### System Status
```bash
GET /api/inventory/v1/status
```
Returns system configuration and status information.

#### Health Check
```bash
GET /api/inventory/v1/health
```
Performs comprehensive health check of the inventory system.

#### Sync Status
```bash
GET /api/inventory/v1/sync-status?date=2024-01-01
```
Returns sync status for a specific date.

## Report Format

The inventory report includes:

- **Date**: Report generation date
- **SKU**: Product stock keeping unit (trade name)
- **Product Name**: Full product name
- **Strength**: Product strength information
- **Opening Balance**: Starting inventory level (in grams)
- **Quantity Dispensed**: Amount dispensed during the day (in grams)
- **Closing Balance**: Ending inventory level (in grams)

## Testing the Implementation

1. **Check System Status**:
   ```bash
   curl -X GET http://localhost:50000/api/inventory/v1/status
   ```

2. **Perform Health Check**:
   ```bash
   curl -X GET http://localhost:50000/api/inventory/v1/health
   ```

3. **Send Test Report**:
   ```bash
   curl -X POST http://localhost:50000/api/inventory/v1/test-report
   ```

4. **Generate Daily Report**:
   ```bash
   curl -X POST http://localhost:50000/api/inventory/v1/trigger-daily-report-image
   ```

## Cron Job Setup

To automate daily reports, add this to your cron configuration:

```bash
# Daily inventory report at midnight
0 0 * * * curl -X POST https://your-domain.com/api/inventory/v1/trigger-daily-report-image
```

## Report Types

The system uses dedicated inventory report types:

- `INVENTORY_DAILY`: Daily inventory reports
- `INVENTORY_WEEKLY`: Weekly inventory reports (future enhancement)
- `INVENTORY_MONTHLY`: Monthly inventory reports (future enhancement)

These are stored separately from sales reports in the S3 bucket under the `inventory-reports/` folder structure.

## Data Flow

### Daily Snapshot Creation Process
1. **Date Determination**: Target date (usually yesterday for completed day reporting)
2. **Order Collection**: Fetch orders for the specific date from WordPress API
3. **Quantity Calculation**: Process orders to determine dispensed quantities per product
4. **Balance Retrieval**: Get current product balances from `productstock` table
5. **Snapshot Calculation**:
   - Opening Balance = Current Balance + Daily Dispensed (backtrack calculation)
   - Quantity Dispensed = Calculated from day's orders
   - Closing Balance = Current Balance (what we have now)
6. **Database Storage**: Save snapshot to `daily_inventory_snapshots` table
7. **Validation**: Ensure Opening - Dispensed = Closing balance

### Report Generation Process
1. **Snapshot Retrieval**: Get existing snapshot data for the report date
2. **Data Formatting**: Transform snapshot data into report format
3. **HTML Generation**: Create professional HTML template (no SKU codes shown)
4. **Image Creation**: Use Puppeteer to generate PNG from HTML
5. **S3 Storage**: Upload image under `inventory-reports/inventory-daily/YYYY/MM/` structure
6. **Slack Notification**: Send image to designated Slack channel

### Key Benefits
- **Historical Accuracy**: Each day's data is preserved exactly as it was
- **Performance**: Fast report generation using pre-calculated snapshots
- **Consistency**: Reports always show the same data for a given date
- **Audit Trail**: Complete history of inventory changes over time

## Snapshot Management

### Understanding Snapshots

Daily snapshots are the foundation of the inventory reporting system. Each snapshot contains:

```sql
-- Example snapshot record
report_date: '2025-07-31'
product_name: 'Choc Frappe'
sku: '25-01/12-02/31-03/02-04/28-05'
strength: '29%'
opening_balance: 1100.00
quantity_dispensed: 8.00
closing_balance: 1092.00
```

### Snapshot Lifecycle

1. **Creation**: Snapshots are created when:
   - Generating a report for a date without existing snapshots
   - Manually triggering backfill operations
   - System detects missing historical data

2. **Validation**: Each snapshot is validated for mathematical accuracy:
   ```
   Opening Balance - Quantity Dispensed = Closing Balance
   ```

3. **Immutability**: Once created, snapshots preserve historical accuracy
   - Use regular backfill for missing data (safe)
   - Use force backfill only when data correction is needed (destructive)

### Checking Snapshot Data

```sql
-- View snapshots for a specific date
SELECT
  product_name,
  opening_balance,
  quantity_dispensed,
  closing_balance,
  (opening_balance - quantity_dispensed) as calculated_closing
FROM daily_inventory_snapshots
WHERE report_date = '2025-07-31'
ORDER BY product_name;

-- Check for missing snapshots
SELECT DISTINCT report_date
FROM daily_inventory_snapshots
WHERE report_date >= '2025-07-01'
ORDER BY report_date;
```

## Troubleshooting

### Common Issues

#### 1. Missing Snapshots
**Problem**: Report shows "No data available" or missing products
**Solution**:
```bash
# Check if snapshots exist for the date
curl -X GET http://localhost:5000/api/inventory/v1/daily-report-data

# Create missing snapshots
curl -X POST http://localhost:5000/api/inventory/v1/backfill-yesterday
```

#### 2. Incorrect Balances
**Problem**: Opening/closing balances don't match expectations
**Solution**:
```bash
# Force recreate snapshots with current data
curl -X POST http://localhost:5000/api/inventory/v1/force-backfill-yesterday
```

#### 3. Mathematical Inconsistencies
**Problem**: Opening - Dispensed ≠ Closing Balance
**Diagnosis**: Check snapshot data in database
```sql
SELECT *, (opening_balance - quantity_dispensed) as calculated_closing
FROM daily_inventory_snapshots
WHERE (opening_balance - quantity_dispensed) != closing_balance;
```

#### 4. No Slack Channel Configured
**Problem**: Reports generate but don't send to Slack
**Solution**:
- Set `SLACK_INVENTORY_REPORT_CHANNEL` environment variable
- Ensure the bot has access to the channel

#### 5. S3 Upload Failures
**Problem**: Reports generate but don't appear in S3
**Solution**:
- Verify AWS credentials and bucket permissions
- Check `AWS_BUCKET_NAME` configuration

#### 6. Image Generation Fails
**Problem**: HTML generates but PNG creation fails
**Solution**:
- Ensure Puppeteer dependencies are installed
- Check server has sufficient memory for image generation

### Logs

Monitor logs for detailed error information:
```bash
# Check application logs for inventory reporting
grep "inventory" /path/to/your/logs/app.log
```

## Integration with Medicine Register

The inventory reporting system is built as a complementary system to the existing medicine register:

### Data Sources Integration
- **WordPress API**: Uses the same order data endpoints for consistency
- **ProductStock Table**: References current balances from the same source
- **Order Processing**: Uses identical order processing logic for dispensed quantities

### Key Differences from Medicine Register
- **Historical Focus**: Creates permanent snapshots vs. real-time calculations
- **Reporting Purpose**: Optimized for daily inventory reports vs. medicine register documents
- **Data Persistence**: Stores historical data vs. transient calculations
- **User Interface**: Professional reports vs. detailed medicine register forms

### Benefits of Dual System Approach
- **Data Consistency**: Both systems use identical source data
- **Performance**: Reports use pre-calculated snapshots for speed
- **Audit Trail**: Historical snapshots provide complete inventory history
- **Flexibility**: Medicine register for operational needs, inventory reports for management

### Synchronization
The systems stay synchronized through:
- Shared data sources (WordPress API, productstock table)
- Identical order processing algorithms
- Real-time balance updates reflected in both systems

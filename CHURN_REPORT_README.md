# Churn Report System Documentation

## Overview

The Churn Report System is a comprehensive analytics tool designed to identify and analyze patient churn patterns in a medical cannabis treatment service. The system processes treatment plan data and order history to identify patients who have stopped using the service and provides detailed insights into churn patterns and risk factors.

## Table of Contents

1. [Data Sources](#data-sources)
2. [Data Structures](#data-structures)
3. [Processing Logic](#processing-logic)
4. [Churn Detection Algorithm](#churn-detection-algorithm)
5. [Report Generation](#report-generation)
6. [API Endpoints](#api-endpoints)
7. [Configuration](#configuration)
8. [Dependencies](#dependencies)

## Data Sources

### Primary Data Sources

The churn report system relies on two main external data sources accessed through WordPress API endpoints:

#### 1. Treatment Plans Data (`/sync/v1/tpdata`)
- **Source:** WordPress site at `letsroll.harvest.delivery`
- **Endpoint:** `{WP_API_URL}/sync/v1/tpdata`
- **Authentication:** JWT token-based authentication
- **Data Type:** JSON response containing treatment plan information
- **Key Fields:**
  - `Treatment Plans Start Date`
  - `Treatment Plans End Date`
  - `user_email`
  - `user_phone`
  - `zoho_contact_id`
  - `first_name`
  - `last_name`
  - `user_company`
  - `user_address_1`
  - `user_address_2`
  - `user_city`
  - `user_state`
  - `user_postcode`
  - `user_country`

#### 2. Order Data (`/sync/v1/orderdata`)
- **Source:** WordPress site at `letsroll.harvest.delivery`
- **Endpoint:** `{WP_API_URL}/sync/v1/orderdata?start_date={start}&end_date={end}&status={status}`
- **Authentication:** JWT token-based authentication
- **Data Type:** JSON response containing order history
- **Key Fields:**
  - `user_email`
  - `order_date_created`
  - `user_phone`
  - `zoho_contact_id`
  - `first_name`
  - `last_name`
  - `user_company`
  - `user_address_1`
  - `user_address_2`
  - `user_city`
  - `user_state`
  - `user_postcode`
  - `user_country`

### Data Source Configuration

```typescript
// Environment variables required
WP_API_URL: string          // WordPress API base URL
WP_USERNAME: string         // WordPress username for authentication
WP_PASSWORD: string         // WordPress password for authentication
```

## Data Structures

### Core Data Types

#### GeneralObject
Represents the base data structure for both treatment plans and orders:

```typescript
export type GeneralObject = {
  id: string;
  date: Date;
  amount: number;
  user_email: string;
  order_date_created: string;
  user_phone: string;
  zoho_contact_id: string;
  first_name: string;
  last_name: string;
  user_company: string;
  user_address_1: string;
  user_address_2: string;
  user_city: string;
  user_state: string;
  user_postcode: string;
  user_country: string;
};
```

#### PatientResult
Represents a churned patient with detailed analysis:

```typescript
export type PatientResult = {
  email: string;
  orderCount: number;
  orderDates: string[];
  firstRepeatStart: string;
  firstRepeatEnd: string;
  tpStart: string;
  tpEnd: string;
  tpExpired: boolean;
  phone: string;
  usecase: string;
  zohoID: string;
  module: string;
  fullName: string;
  churnedConfidence: 'high' | 'low' | '';
  daysSinceLastOrder: string;
  userCompany: string;
  userAddress_1: string;
  userAddress_2: string;
  userCity: string;
  userState: string;
  userPostcode: string;
  userCountry: string;
};
```

## Processing Logic

### Date Range Calculation

The system uses a sliding window approach to analyze churn patterns:

```typescript
export const findStartAndEndDate = (today: DateTime): { start: string; end: string } => {
  const startDate = today.minus({ days: 35 });
  const endDate = startDate.plus({ days: 7 });
  
  return {
    start: startDate.toFormat('yyyy-MM-dd'),
    end: endDate.toFormat('yyyy-MM-dd'),
  };
};
```

**Logic:**
- Analyzes treatment plans that ended between 35-42 days ago
- This window captures patients who should have renewed their treatment plans
- Allows for a 7-day grace period after treatment plan expiration

### Treatment Plan Filtering

```typescript
const targetTps = tps.data.filter((e) => {
  // Must have valid start and end dates
  if (!e['Treatment Plans Start Date'] || !e['Treatment Plans End Date']) {
    return false;
  }

  const tpStartDate = DateTime.fromISO(e['Treatment Plans Start Date'], { zone: 'utc' });
  const tpEndDate = DateTime.fromISO(e['Treatment Plans End Date'], { zone: 'utc' });
  const start = DateTime.fromISO(targetDates.start, { zone: 'utc' });
  const end = DateTime.fromISO(targetDates.end, { zone: 'utc' });

  // Treatment plan must overlap with target period
  return (
    tpEndDate.startOf('day') >= start.startOf('day') &&
    tpStartDate.startOf('day') <= end.startOf('day') &&
    tpStartDate.startOf('day') >= startingDataPoint.startOf('day')
  );
});
```

## Churn Detection Algorithm

### Core Churn Detection Logic

The `findChurnedPatients` function implements the main churn detection algorithm:

```typescript
export const findChurnedPatients = (tps: GeneralObject[], orders: GeneralObject[]): PatientResult[] => {
  // 1. Preprocess orders by user email
  const ordersMap = new Map<string, Date[]>();
  orders.forEach((order) => {
    const email = order.user_email.toLowerCase().trim();
    const date = new Date(order.order_date_created);
    if (!ordersMap.has(email)) ordersMap.set(email, []);
    ordersMap.get(email)!.push(date);
  });

  // 2. Sort all order dates for each patient
  ordersMap.forEach((dates) => dates.sort((a, b) => a.getTime() - b.getTime()));

  const churnedPatients: PatientResult[] = [];

  tps.forEach((patient) => {
    const email = patient.user_email.toLowerCase().trim();
    const patientOrders = ordersMap.get(email);
    if (!patientOrders || patientOrders.length === 0) return;

    // 3. Calculate key dates
    const tpStart = new Date(patient['Treatment Plans Start Date']);
    const tpEnd = new Date(patient['Treatment Plans End Date']);
    const firstRepeatStart = new Date(patientOrders[0]);
    const firstRepeatEnd = new Date(firstRepeatStart);
    firstRepeatEnd.setDate(firstRepeatEnd.getDate() + 35); // 28 + 7 days

    // 4. Churn detection criteria
    const allInFirstRepeat = patientOrders.every(
      (orderDate) => orderDate >= firstRepeatStart && orderDate <= firstRepeatEnd,
    );
    const hasOrdersAfterFirstRepeat = patientOrders.some((orderDate) => orderDate > firstRepeatEnd);

    // 5. Treatment plan expiration check
    const today = DateTime.utc();
    const tpEndDate = DateTime.fromISO(patient['Treatment Plans End Date'], { zone: 'utc' });
    const hasTpExpired = tpEndDate.startOf('day') <= today.startOf('day');

    // 6. Churn confidence calculation
    const lastOrder = patientOrders[patientOrders.length - 1];
    const lastOrderDate = DateTime.fromISO(lastOrder.toISOString().split('T')[0]);
    const daysSinceLastOrder = today.diff(lastOrderDate, 'days').days;
    
    let churnedConfidence: 'high' | 'low' | '' = '';
    if (daysSinceLastOrder > 28) {
      churnedConfidence = 'high';
    } else {
      churnedConfidence = 'low';
    }

    // 7. Final churn determination
    if (allInFirstRepeat && !hasOrdersAfterFirstRepeat) {
      churnedPatients.push({
        // ... patient data with churn analysis
      });
    }
  });

  // 8. Sort by treatment plan start date (oldest first)
  return churnedPatients.sort((a, b) => {
    const dateA = DateTime.fromISO(a.tpStart);
    const dateB = DateTime.fromISO(b.tpStart);
    return dateA.toMillis() - dateB.toMillis();
  });
};
```

### Churn Detection Criteria

A patient is considered "churned" if they meet **ALL** of the following criteria:

1. **First Repeat Period Only:** All orders must fall within the first repeat period (35 days from first order)
2. **No Orders After First Repeat:** No orders exist after the first repeat period ends
3. **Valid Treatment Plan:** Must have a valid treatment plan with start and end dates
4. **Order History:** Must have at least one order in their history

### Churn Confidence Levels

- **High Confidence:** More than 28 days since last order
- **Low Confidence:** 28 days or less since last order

## Report Generation

### Key Metrics Calculated

1. **Churn Rate:** Percentage of patients who churned (excluding expired treatment plans)
2. **High Confidence Churn Rate:** Percentage of patients with high confidence churn
3. **Patient Categories:**
   - **Churned Patients:** Patients who meet churn criteria
   - **Committed Patients:** Patients who placed orders after their first repeat period
   - **Expired Treatment Plans:** Patients whose treatment plans have expired

### Slack Report Structure

The system generates a comprehensive Slack report with:

```typescript
const buildSlackReport = () => {
  const blocks = [
    {
      type: 'header',
      text: `📊 CHURN Report for ${today}`,
    },
    {
      type: 'context',
      text: `Find detailed reporting here ${googleSheetURL}`,
    },
    {
      type: 'section',
      text: '*Key Metrics*',
    },
    // Additional metrics and patient lists
  ];
};
```

### Report Components

1. **Header:** Report date and title
2. **Google Sheets Link:** Detailed data export
3. **Key Metrics:**
   - Total patients analyzed
   - Churn rate percentage
   - High confidence churn rate
   - Number of expired treatment plans
4. **Patient Lists:**
   - Churned patients with contact information
   - Committed patients
   - Expired treatment plans

## API Endpoints

### Main Churn Report Endpoint

```typescript
POST /api/report/v1.0/churn-report
```

**Request Body:**
```typescript
{
  sheetURL?: string;        // Google Sheets URL for detailed export
  startingDate?: string;    // Optional start date filter (yyyy-MM-dd)
  text?: string;           // Optional report date (yyyy-MM-dd)
}
```

**Response:**
```typescript
{
  success: boolean;
  message: string;
  data: {
    requests: PatientResult[];
    counts: {
      all: number;
      requests: number;
      unread: number;
    };
  };
}
```

## Configuration

### Environment Variables

```bash
# WordPress API Configuration
WP_API_URL=https://letsroll.harvest.delivery/wp-json
WP_USERNAME=your_username
WP_PASSWORD=your_password

# Google Sheets Integration
GOOGLE_SHEET=your_google_sheet_url

# Slack Integration
SLACK_TOKEN=your_slack_token
SLACK_REPORT_CHANNEL=your_channel_id
```

### Date Configuration

- **Analysis Window:** 35-42 days (configurable in `findStartAndEndDate`)
- **First Repeat Period:** 35 days (28 + 7 days grace period)
- **High Confidence Threshold:** 28 days since last order
- **Treatment Plan Expiration:** Based on `Treatment Plans End Date`

## Dependencies

### Core Dependencies

```json
{
  "luxon": "^3.0.0",           // Date/time manipulation
  "express": "^4.18.0",        // Web framework
  "axios": "^1.0.0",           // HTTP client for WordPress API
  "pg": "^8.0.0"              // PostgreSQL client
}
```

### External Services

1. **WordPress Site:** `letsroll.harvest.delivery`
   - Custom REST API endpoints
   - JWT authentication
   - Treatment plan and order data

2. **Google Sheets:** Detailed data export and analysis

3. **Slack:** Report delivery and notifications

4. **PostgreSQL Database:** Local data storage and caching

## Data Flow Summary

1. **Data Retrieval:** Fetch treatment plans and orders from WordPress API
2. **Date Filtering:** Filter data based on configurable date ranges
3. **Churn Analysis:** Apply churn detection algorithm to identify churned patients
4. **Metrics Calculation:** Calculate churn rates and confidence levels
5. **Report Generation:** Create Slack report with key metrics and patient lists
6. **Data Export:** Provide Google Sheets link for detailed analysis

## Business Logic

### Treatment Plan Lifecycle

1. **Treatment Plan Start:** Patient begins treatment
2. **First Order:** Initial order placed
3. **First Repeat Period:** 35-day window for repeat orders
4. **Treatment Plan End:** Official end date of treatment plan
5. **Churn Detection:** Analysis of patient behavior after treatment plan ends

### Churn Risk Factors

1. **Early Churn:** Patients who only order during first repeat period
2. **Treatment Plan Expiration:** Patients whose plans have expired
3. **Order Frequency:** Patients with low order frequency
4. **Time Since Last Order:** Patients with extended periods without orders

This system provides comprehensive insights into patient retention and helps identify opportunities for intervention to reduce churn rates. 